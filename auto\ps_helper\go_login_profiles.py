import asyncio
from fastapi.responses import HTMLResponse
from dateutil.parser import parse as dparse
from datetime import datetime, timedelta, timezone
from postgres import postgrest_client
import requests

GOLOGIN_API_URL = "https://api.gologin.com/browser/v2"
AUTH_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RpZCI6IjY3M2UwYzc5MzZiOTI1NWRlODllYjhhZiIsInR5cGUiOiJ1c2VyIiwic3ViIjoiNjcyMzhkMjY2ZjAxMWIwMTg4ZjZlMDg3In0.R5tnrhoPiPBXBzoVQ666MIlj7VazMYJTncqeob6mBtc"
workspace_id = "67238d266f011b0188f6e094"


async def get_profile_id_by_email(email):
    url = f"https://api.gologin.com/workspaces/{workspace_id}/profiles"
    params = {
        "search": email,
        "limit": 3000,
        "offset": 0,
        "sortField": "order",
        "sortOrder": "descend",
    }

    headers = {
        "Authorization": f"Bearer {AUTH_KEY}",
        "Content-Type": "application/json",
    }

    response = requests.get(url, headers=headers, params=params)
    if response.status_code == 200:
        profile = response.json()['profiles']
        if profile:
            return profile
    else:
        print(f"Failed to fetch profiles: {response.status_code} {response.text}")
    return None

async def index():
    accounts = await postgrest_client.get_all_accounts()
    return accounts

async def get_one_by_email(email):
    accounts = await index()
    for a in accounts:
        if email == a['email']:
            print(a)
            profile_id, _ = await get_profile_id_by_email(email)
            data = {
                'id': a['id'],
                'email': email,
                'password': a['password'],
                'profile id': profile_id,
            }
            return data
    print("No profile found")

async def get_id(email):
    pass

# async def main():
#     # print(await get_profile_id_by_email("<EMAIL>"))
#     profile = (await get_profile_id_by_email("<EMAIL>"))
#     print(profile)
#     print("---------")
#     await get_one_by_email("<EMAIL>")


# asyncio.run(main())
