import imaplib
import email as pymail
import datetime, pytz
import time
import re

MAIL_DOMAIN='ampgams.com'
MAIL_URL='box.ampgams.com'
ADMIN_PASSWORD='wonderfulendrega'
USER_PASSWORD='irritablenightwraith'
MAIL_ADMIN_URL=f'https://{MAIL_URL}/admin/mail'
ADMIN_EMAIL=f'admin@{MAIL_DOMAIN}'

def get_user_inbox(email, password=USER_PASSWORD, search_criterion="ALL"):
    """
    Returns array containing inbox for a given email in form
    [
            {
                    'from': $SENDER,
                    'subject': $EMAIL_SUBJECT,
                    'content': $EMAIL_BODY
            },
            ...
    ]
    To restrict results for a given criterion, pass the kwarg search_criterion, e.g.
            search_criterion='(FROM "<EMAIL>")'
    """
    inbox = []
    mail = imaplib.IMAP4_SSL(MAIL_URL)
    mail.login(email, password)
    mail.select("inbox")
    mail_ids = []
    status, data = mail.search(None, search_criterion)
    for block in data:
        mail_ids.extend(block.split())
    for id in mail_ids:
        status, data = mail.fetch(id, "(RFC822)")
        for response_part in data:
            if isinstance(response_part, tuple):
                message = pymail.message_from_bytes(response_part[1])
                mail_from = message["from"]
                mail_subject = message["subject"]
                received = message["Date"]
                received_datetime = pymail.utils.parsedate_to_datetime(received)
                if message.is_multipart():
                    mail_content = ""
                    for part in message.get_payload():
                        if part.get_content_type() == "text/plain":
                            payload = part.get_payload(decode=True)
                            charset = part.get_content_charset()
                            if charset:
                                payload = payload.decode(charset)
                            mail_content += payload
                else:
                    payload = message.get_payload(decode=True)
                    charset = message.get_content_charset()
                    if charset:
                        mail_content = payload.decode(charset)
                inbox.append(
                    {
                        "from": mail_from,
                        "subject": mail_subject,
                        "content": mail_content,
                        "received_datetime": received_datetime,
                    }
                )
    return inbox

def get_user_spam(email, password=USER_PASSWORD, search_criterion="ALL"):
    """
    Returns array containing inbox for a given email in form
    [
            {
                    'from': $SENDER,
                    'subject': $EMAIL_SUBJECT,
                    'content': $EMAIL_BODY
            },
            ...
    ]
    To restrict results for a given criterion, pass the kwarg search_criterion, e.g.
            search_criterion='(FROM "<EMAIL>")'
    """
    inbox = []
    mail = imaplib.IMAP4_SSL(MAIL_URL)
    mail.login(email, password)
    mail.select("Spam")
    mail_ids = []
    status, data = mail.search(None, search_criterion)
    for block in data:
        mail_ids.extend(block.split())
    for id in mail_ids:
        status, data = mail.fetch(id, "(RFC822)")
        for response_part in data:
            if isinstance(response_part, tuple):
                message = pymail.message_from_bytes(response_part[1])
                mail_from = message["from"]
                mail_subject = message["subject"]
                received = message["Date"]
                received_datetime = pymail.utils.parsedate_to_datetime(received)
                if message.is_multipart():
                    mail_content = ""
                    for part in message.get_payload():
                        if part.get_content_type() == "text/plain":
                            payload = part.get_payload(decode=True)
                            charset = part.get_content_charset()
                            if charset:
                                payload = payload.decode(charset)
                            mail_content += payload
                else:
                    mail_content = message.get_payload(decode=True)
                    charset = message.get_content_charset()
                    if charset:
                        mail_content = payload.decode(charset)
                inbox.append(
                    {
                        "from": mail_from,
                        "subject": mail_subject,
                        "content": mail_content,
                        "received_datetime": received_datetime,
                    }
                )
    return inbox
def get_email_verification_code(email):
    start_time = datetime.datetime.now(tz=pytz.timezone('Europe/London'))
    time_threshold = 90 * 60
    trycount = 0
    code = None
    while code is None:
        if trycount > 120:
            raise Exception("Did not receive verification email")
        inbox_emails = get_user_inbox(email, search_criterion='(FROM "Sony")')
        spam_emails = get_user_spam(email, search_criterion='(FROM "Sony")')
        emails = sorted(
            inbox_emails + spam_emails, key=lambda d: d["received_datetime"]
        )
        latest_email = emails[-1]
        content = latest_email["content"]
        received_datetime = latest_email["received_datetime"].astimezone(pytz.utc)
        age = (received_datetime - start_time).total_seconds()
        if abs(age) > time_threshold:
            trycount += 1
            time.sleep(1)
            continue
        match = re.search(
            r"El código de verificación para su cuenta de Sony es:\s+(?P<code>\d+)",
            content,
            flags=re.MULTILINE,
        )
        if match:
            code = match.group("code")
            return code
        trycount += 1
        time.sleep(30)
# Modify the function to accept a logging callback
def get_email_verification_code_sse(email, log_callback):
    start_time = datetime.datetime.now(tz=pytz.timezone('Europe/London'))
    log_callback(f"start_time: {start_time}")
    time_threshold = 90 * 60
    trycount = 0
    code = None
    log_callback(f"getting email for {email}")

    while code is None:
        if trycount > 120:
            log_callback("Did not receive verification email")
            return None
        # Simulated email fetching (replace with actual function calls)
        inbox_emails = get_user_inbox(email, search_criterion='(FROM "Sony")')
        spam_emails = get_user_spam(email, search_criterion='(FROM "Sony")')
        emails = sorted(
            inbox_emails + spam_emails, key=lambda d: d["received_datetime"]
        )

        # If there are no emails, continue
        if not emails:
            trycount += 1
            time.sleep(1)
            continue
        latest_email = emails[-1]
        content = latest_email["content"]
        received_datetime = latest_email["received_datetime"].astimezone(pytz.utc)
        age = (received_datetime - start_time).total_seconds()

        if abs(age) > time_threshold:
            log_callback(f"got an old email: {received_datetime}, age: {age}")
            trycount += 1
            time.sleep(1)
            continue
        log_callback(f"got email, received: {received_datetime}")

        match = re.search(
            r"El código de verificación para su cuenta de Sony es:\s+(?P<code>\d+)",
            content,
            flags=re.MULTILINE,
        )
        if match:
            code = match.group("code")
            log_callback(f"Got the code: {code}")
            return code
        trycount += 1
        time.sleep(30)

def get_email_confirmation_link(email):
    start_time = datetime.datetime.now(tz=pytz.utc)
    time_threshold = 90 * 60
    trycount = 0
    code = None
    while code is None:
        if trycount > 120:
            raise Exception("Did not receive confirmation link")
        inbox_emails = get_user_inbox(email, search_criterion='(FROM "Sony")')
        spam_emails = get_user_spam(email, search_criterion='(FROM "Sony")')
        emails = sorted(
            inbox_emails + spam_emails, key=lambda d: d["received_datetime"]
        )
        latest_email = emails[-1]
        content = latest_email["content"]
        received_datetime = latest_email["received_datetime"].astimezone(pytz.utc)
        age = (received_datetime - start_time).total_seconds()
        if abs(age) > time_threshold:
            trycount += 1
            time.sleep(1)
            continue
        match = re.search(
            r"https://my\.account\.sony\.com.+",
            content
        )
        if match:
            link = match.group(0)
            return link
        trycount += 1
        time.sleep(30)

def get_email_verification_link(email):
    print(f"getting email for {email}")
    start_time = datetime.datetime.now(tz=pytz.utc)
    time_threshold = 90 * 60  # 90 minutes in seconds
    link = None
    trycount = 0
    while link is None:
        if trycount > 36:
            raise Exception("Did not receive verification email")

        inbox_emails = get_user_inbox(email, search_criterion='(FROM "Sony")')
        spam_emails = get_user_spam(email, search_criterion='(FROM "Sony")')
        emails = sorted(
            inbox_emails + spam_emails, key=lambda d: d["received_datetime"]
        )

        if not emails:
            print("No emails found, retrying...")
            trycount += 1
            time.sleep(10)
            continue

        latest_email = emails[-1]
        content = latest_email["content"]
        received_datetime = latest_email["received_datetime"].astimezone(pytz.utc)
        age = (received_datetime - start_time).total_seconds()

        # Skip old emails
        if abs(age) > time_threshold:
            print(f"Skipping old email from {received_datetime}, age: {age} seconds")
            trycount += 1
            time.sleep(10)
            continue

        print(f"Checking email received at: {received_datetime}")

        # Save full content to a file for debugging
        try:
            with open(f"email_content_{trycount}.html", "w", encoding="utf-8") as f:
                f.write(content)
            print(f"Saved full email content to email_content_{trycount}.html")
        except Exception as e:
            print(f"Failed to save email content: {e}")

        # SIMPLEST APPROACH: Find all URLs in the content
        all_urls = re.findall(r'https://[^\s]+', content)

        if all_urls:
            print(f"Found {len(all_urls)} URLs in the email")

            # First, try to find the most likely verification URL
            for url in all_urls:
                # Look specifically for the verification URL pattern we've seen
                if "my.account.sony.com/central/verification" in url:
                    link = url
                    print(f"Found verification URL: {link}")
                    return link

            # If no verification URL found, look for URLs near "Recupera tu cuenta"
            recupera_index = content.find("Recupera tu cuenta")
            if recupera_index != -1:
                print("Found 'Recupera tu cuenta' text, looking for nearby URLs")
                # Look for URLs in the vicinity (500 chars before and after)
                start_pos = max(0, recupera_index - 500)
                end_pos = min(len(content), recupera_index + 500)
                vicinity_text = content[start_pos:end_pos]

                # Find all URLs in this vicinity
                vicinity_urls = re.findall(r'https://[^\s]+', vicinity_text)
                if vicinity_urls:
                    # Use the URL closest to "Recupera tu cuenta" text
                    link = vicinity_urls[0]
                    print(f"Found URL near 'Recupera tu cuenta': {link}")
                    return link

            # If still no link found, try the v1dys-button-a class
            match = re.search(
                r'<a class="v1dys-button-a" href="(https://[^"]+)"',
                content,
                flags=re.MULTILINE | re.DOTALL,
            )
            if match:
                link = match.group(1)
                print(f"Found link with v1dys-button-a class: {link}")
                return link

            # Last resort: just use the longest URL that looks like a verification link
            verification_candidates = []
            for url in all_urls:
                if any(keyword in url.lower() for keyword in ["account.sony", "playstation", "verification", "reset_password"]):
                    verification_candidates.append(url)

            if verification_candidates:
                # Sort by length (longest first) as verification URLs tend to be long
                verification_candidates.sort(key=len, reverse=True)
                link = verification_candidates[0]
                print(f"Using longest verification-like URL: {link}")
                return link

            # If all else fails, just use the longest URL
            all_urls.sort(key=len, reverse=True)
            link = all_urls[0]
            print(f"Using longest URL as fallback: {link}")
            return link

        print(f"No URLs found in attempt {trycount}")
        trycount += 1
        time.sleep(10)


if __name__ == "__main__":
    link = get_email_verification_link('<EMAIL>')
    print(link)