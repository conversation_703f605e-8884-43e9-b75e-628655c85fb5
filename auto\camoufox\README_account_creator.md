# PlayStation Account Creator

An automated PlayStation account creation tool using CamouFox browser automation with identity generation, email verification, and captcha handling.

## Features

- **Automated Identity Generation**: Creates realistic identities with names, dates of birth, and location data
- **Email Account Setup**: Automatically creates email accounts for verification
- **CamouFox Browser Automation**: Uses humanized browser automation with proxy support
- **Captcha Handling**: Pauses for manual captcha solving when required
- **Email Verification**: Automatically retrieves and processes verification emails
- **SSO Code Extraction**: Extracts PlayStation SSO codes for account management
- **Database Integration**: Saves created accounts to PostgreSQL database
- **Proxy Management**: Rotates through available proxies for account creation

## Files

- `ps_account_creator.py` - Main account creation automation script
- `test_account_creator.py` - Test script with interactive menu
- `README_account_creator.md` - This documentation file

## Dependencies

The script relies on existing modules in the codebase:

- `camoufox` - Browser automation library
- `auto.ps_helper.create_identity` - Identity generation
- `auto.ps_helper.mail_util` - Email account management
- `auto.ps_helper.postgres` - Database operations
- `auto.camoufox.proxy_manager` - Proxy management
- `auto.camoufox.mail` - Email verification utilities

## Usage

### Basic Usage

```python
from ps_account_creator import create_playstation_account

# Create a single account with automatic proxy selection
success = create_playstation_account()

if success:
    print("Account created successfully!")
else:
    print("Account creation failed!")
```

### Advanced Usage

```python
from ps_account_creator import create_playstation_account

# Create account with specific proxy
proxy = {
    "server": "http://proxy.example.com:8080",
    "username": "proxy_user",
    "password": "proxy_pass"
}

success = create_playstation_account(proxy=proxy)
```

### Using the Test Script

Run the interactive test script:

```bash
python auto/camoufox/test_account_creator.py
```

This provides a menu with options to:
1. Test identity generation
2. Test email setup
3. Create single account
4. Create multiple accounts
5. Custom batch creation

## Account Creation Process

The automation follows these steps:

1. **Identity Generation**
   - Generates random name, date of birth, location
   - Creates unique email address
   - Generates secure password

2. **Email Setup**
   - Creates email account on mail server
   - Prepares for verification emails

3. **Browser Setup**
   - Creates CamouFox profile with proxy
   - Configures humanized browsing behavior
   - Sets up fingerprint protection

4. **Account Creation**
   - Navigates to PlayStation website
   - Clicks sign-in and create account
   - Fills registration form automatically
   - Handles captcha (manual intervention)
   - Processes email verification

5. **Post-Creation**
   - Extracts SSO code
   - Saves account to database
   - Returns success status

## Manual Intervention Points

The script will pause and require manual intervention for:

1. **Captcha Solving**: When captcha appears, solve it manually and press Enter
2. **Email Verification**: If automatic email processing fails, manually click verification link

## Configuration

### Proxy Configuration

Proxies are managed by `auto.camoufox.proxy_manager`. The script automatically:
- Rotates through available proxies
- Tracks proxy success/failure rates
- Retries with new proxy on connection failure

### Identity Configuration

Identities are generated with Mexican location data by default:
- City: Ciudad de México
- Province: Distrito Federal
- Random postal codes from Mexico City area
- Birth years: 1980-2000

### Email Configuration

Email accounts are created on the `ampgams.com` domain with:
- Random 3-word identifiers
- Automatic inbox monitoring
- Spam folder checking

## Error Handling

The script includes comprehensive error handling for:

- Network connection issues
- Proxy failures
- Page loading timeouts
- Element not found errors
- Email verification timeouts
- Database connection issues

## State Machine

The account creation uses a state machine with these states:

- `ON_HOME_PAGE` - PlayStation homepage
- `CAN_CLICK_SIGN_IN` - Sign-in button visible
- `CAN_CLICK_CREATE_ACCOUNT` - Create account link visible
- `ON_REGISTRATION_FORM` - Registration form loaded
- `CAPTCHA_REQUIRED` - Captcha needs solving
- `EMAIL_VERIFICATION_REQUIRED` - Email verification needed
- `ACCOUNT_CREATED` - Account successfully created
- `ERROR_PAGE` - Error occurred

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check internet connection
   - Verify proxy configuration
   - Try different proxy

2. **Captcha Issues**
   - Solve captcha manually when prompted
   - Press Enter to continue after solving

3. **Email Verification Delays**
   - Wait up to 10 minutes for verification email
   - Check spam folder
   - Manually click verification link if needed

4. **Database Errors**
   - Verify PostgreSQL connection
   - Check database credentials
   - Ensure tables exist

### Debug Mode

For debugging, you can modify the script to:
- Enable verbose logging
- Use headful browser mode
- Add breakpoints at specific states

## Security Considerations

- Uses humanized browsing patterns to avoid detection
- Rotates proxies to distribute requests
- Implements delays between actions
- Uses realistic identity data
- Maintains separate browser profiles

## Performance

- Single account creation: ~5-10 minutes
- Batch creation: Add 30-second delays between accounts
- Success rate: Depends on proxy quality and captcha solving
- Memory usage: ~200MB per browser instance

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review error messages in console output
3. Test individual components using the test script
4. Verify all dependencies are properly installed
