from __future__ import annotations
import random
import datetime
from human_id import generate_id
from pprint import pprint

from dataclasses import dataclass, asdict


class PostCodeGen:
	_codes = [
		'01030', '01070', '01090', '01100', '01109', '01120', '01125', '01130', '01139', '01140', '01150', '01160',
		'01170', '01180', '01184', '01209', '01280', '01289', '01291', '01297', '01329', '01357', '01359', '01388',
		'01401', '01419', '01420', '01460', '01470', '01537', '01567', '01580', '01690', '01788', '01799', '01900',
		'02000', '02010', '02020', '02040', '02050', '02060', '02070', '02080', '02090', '02099', '02110', '02125',
		'02129', '02140', '02160', '02169', '02200', '02230', '02240', '02250', '02300', '02310', '02320', '02330',
		'02340', '02360', '02460', '02470', '02480', '02490', '02500', '02510', '02519', '02520', '02530', '02540',
		'02600', '02630', '02640', '02650', '02660', '02670', '02680', '02700', '02720', '02729', '02730', '02760',
		'02770', '02780', '02790', '02800', '02810', '02820', '02830', '02840', '02850', '02860', '02870', '02900',
		'02910', '02920', '02930', '02940'
	]

	def __new__(cls) -> str:
		return random.choice(cls._codes)
	
class DoBGen:
	def __new__(cls) -> datetime.datetime:
		return datetime.date(random.randint(1980, 2000), random.randint(1, 12), random.randint(1, 28))
	
@dataclass
class Name:
	first_name: str
	last_name: str

class NameGen:
	_firstnames = [
		'Jose', 'Luis', 'Francisco', 'Jorge', 'Miguel', 'Carlos', 'Pedro', 'Manuel', 'Victor', 'Antonio', 'Alejandro', 
		'Mario', 'Roberto', 'Ricardo', 'Fernando', 'Javier', 'Sergio', 'Martin', 'Oscar', 'Daniel'
	]
	_lastnames = [
		'Hernandez', 'Garcia', 'Martinez', 'Lopez', 'Gonzalez', 'Perez', 'Rodriguez', 'Sanchez', 'Ramirez', 'Cruz', 
		'Flores', 'Gomez', 'Morales', 'Vazquez', 'Reyes', 'Jimenez', 'Torres', 'Diaz', 'Gutierrez', 'Ruiz'
	]
	def __new__(cls) -> Name:
		return Name(first_name=random.choice(cls._firstnames), last_name=random.choice(cls._lastnames))

@dataclass
class Identity:
	name: Name
	dob: datetime.datetime
	city: str
	province: str
	postcode: str
	email: str
	username: str
	password: str
	id: int = None

	def dict(self) -> dict:
		d = asdict(self)
		d.pop('name')
		d.pop('id')
		d['firstname'] = self.name.first_name
		d['lastname'] = self.name.last_name
		d['dob'] = self.dob.strftime('%Y-%m-%d')
		d['city'] = self.city
		d['province'] = self.province
		d['postcode'] = self.postcode
		return d

class IdentityGen:
	def __new__(cls) -> Identity:
		name = NameGen()
		dob = DoBGen()
		city = 'Ciudad de México'
		province = 'Distrito Federal'
		postcode = PostCodeGen()
		length = 16
		while length > 15:
			id = generate_id(word_count=3, separator='.')
			length = len(id)
		email = f"{id}@ampgams.com"
		username = email.split('@')[0].replace('.', '') + str(random.randint(100, 999))
		password = generate_id(separator='', word_count=3)[:8] + 'A1!'

		identity = Identity(
			name=name, 
			dob=dob, 
			city=city,
			province=province,
			postcode=postcode, 
			email=email, 
			username=username,
			password=password
		)
		# with open("identity.txt", "w", encoding="utf-8") as f:
		# 	for field, value in identity.dict().items():
		# 		f.write(f"{field}: {value}\n")

		return identity.dict()

		

	
# identity = IdentityGen()
# pprint(identity, sort_dicts=False)