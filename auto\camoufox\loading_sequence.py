import sys
import math
import time
import random
from datetime import datetime, timedelta


# ANSI color codes
RESET = '\033[0m'
BLUE = '\033[38;5;39m'
CYAN = '\033[38;5;51m'
TEAL = '\033[38;5;37m'
GREEN = '\033[38;5;35m'
YELLOW = '\033[38;5;220m'
MAGENTA = '\033[38;5;165m'
RED = '\033[38;5;196m'
ORANGE = '\033[38;5;208m'
PURPLE = '\033[38;5;93m'


# Utility functions for loading animations

def print_loading_header(duration):
    """
    Print the initial loading message with start and end times.

    Args:
        duration: Duration in seconds
    """
    print(f"Pausing for {format_time_display(duration)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=duration)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")


def format_time_display(seconds):
    """Format seconds into HH:MM:SS display."""
    mins, secs = divmod(int(seconds), 60)
    hours, mins = divmod(mins, 60)
    return f"{hours:02d}:{mins:02d}:{secs:02d}"


def create_animation_space(height):
    """
    Create empty space for the animation.

    Args:
        height: Height of the animation in lines
    """
    for _ in range(height + 2):  # +2 for progress bar and time
        print()


def create_progress_bar(progress, width=40):
    """
    Create a progress bar string.

    Args:
        progress: Progress as a float between 0.0 and 1.0
        width: Width of the progress bar

    Returns:
        String representing the progress bar
    """
    filled_width = int(width * progress)
    return f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (width - filled_width)}]"


def format_progress_info(progress, elapsed, total, remaining):
    """
    Format progress information.

    Args:
        progress: Progress as a float between 0.0 and 1.0
        elapsed: Elapsed time in seconds
        total: Total duration in seconds
        remaining: Remaining time in seconds

    Returns:
        Tuple of (progress_bar, status_line)
    """
    progress_bar = create_progress_bar(progress)
    time_display = format_time_display(remaining) + " remaining"
    percentage = f"{progress * 100:.1f}%"

    status_line = f"{progress_bar} {percentage} ({time_display})"
    elapsed_line = f"Elapsed: {int(elapsed)}s / {total}s"

    return status_line, elapsed_line


def update_animation_frame(frame, height):
    """
    Update the animation frame by moving cursor up and printing the new frame.

    Args:
        frame: List of strings or list of lists representing the frame
        height: Height of the animation in lines
    """
    # Move cursor up to overwrite previous frame
    sys.stdout.write('\033[F' * (height + 2))

    # Print each line of the frame
    for row in frame:
        print(''.join(row) if isinstance(row, list) else row)


def run_animation_loop(duration, interval, height, frame_generator, progress_width=40):
    """
    Run an animation loop.

    Args:
        duration: Duration in seconds
        interval: Time interval between frames in seconds
        height: Height of the animation in lines
        frame_generator: Function that takes (step, progress) and returns a frame
        progress_width: Width of the progress bar (used in format_progress_info)
    """
    # Calculate steps
    steps = int(duration / interval)

    # Print initial message and create space
    print_loading_header(duration)
    create_animation_space(height)

    # Animation loop
    for i in range(steps + 1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = duration - elapsed

        # Create frame
        frame = frame_generator(i, progress)

        # Update display
        update_animation_frame(frame, height)

        # Print progress bar and time
        status_line, elapsed_line = format_progress_info(progress, elapsed, duration, remaining)
        print(status_line)
        print(elapsed_line)

        # Sleep for the interval
        time.sleep(interval)


def sleep_wave(duration=30):
    """
    Display a wave animation loading sequence.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.1
    width = 60
    lines = 3

    # Characters for the wave animation
    wave_chars = ["▁", "▂", "▃", "▄", "▅", "▆", "▇", "█", "▇", "▆", "▅", "▄", "▃", "▂"]

    # Colors for the waves
    COLORS = [BLUE, CYAN, TEAL, GREEN]

    progress_width = 40

    steps = int(total_seconds / interval)

    # Print initial message and create space for animation
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    for _ in range(lines + 2):
        print()

    for i in range(steps+1):
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        frames = []

        for line in range(lines):
            frame = []
            phase_offset = line * 0.5  # Different phase for each line
            speed_factor = 0.15 - (line * 0.03)  # Different speed for each line

            for x in range(width):
                # Calculate wave position with smoother sine wave
                wave_pos = math.sin((i * speed_factor) + (x * 0.2) + phase_offset)
                # Map to character index
                char_idx = int((wave_pos + 1) * (len(wave_chars) - 1) / 2)
                # Add colored character
                color = COLORS[line % len(COLORS)]
                frame.append(f"{color}{wave_chars[char_idx]}{RESET}")

            frames.append(''.join(frame))

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (lines + 2))

        # Print each line of the animation
        for frame in frames:
            print(frame)

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def sleep_orbit(duration=30):
    """
    Display a 2D orbital system animation with planets/stars.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.25
    width = 60
    height = 20  # Taller grid for better circular orbits
    progress_width = 40

    # Orbital elements
    center = "🌞"  # Sun in the center
    planets = ["🪐", "🌎", "🌑", "⭐", "☄️", "✨"]  # Planets/stars to orbit
    planet_colors = [ORANGE, BLUE, CYAN, YELLOW, RED, PURPLE]
    orbit_radii = [9, 7, 5, 12, 10, 3]  # Different orbit radii for each planet

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    # Create space for animation (height + 2 for progress bar and time)
    for _ in range(height + 2):
        print()

    # Center coordinates
    center_x = width // 2
    center_y = height // 2

    for i in range(steps+1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Create orbital system grid
        grid = [[' ' for _ in range(width)] for _ in range(height)]

        # Place center sun
        grid[center_y][center_x] = f"{YELLOW}{center}{RESET}"

        # Add some stars in the background (fixed positions)
        for _ in range(30):
            star_x = random.randint(0, width - 1)
            star_y = random.randint(0, height - 1)
            # Only place stars where there's nothing else
            if grid[star_y][star_x] == ' ' and (abs(star_x - center_x) > 3 or abs(star_y - center_y) > 3):
                # Use different star characters with varying brightness
                star_type = random.choice(['·', '·', '·', '·', '*', '.', '⋆'])
                brightness = random.choice([RESET, RESET, RESET, CYAN, YELLOW, PURPLE])
                grid[star_y][star_x] = f"{brightness}{star_type}{RESET}"

        # Place each planet at its orbital position
        for p, (planet, color, radius) in enumerate(zip(planets, planet_colors, orbit_radii)):
            # Calculate angle based on time and planet speed
            speed = 0.3 - (p * 0.04)  # Different speed for each planet
            angle = (i * speed) + (p * (math.pi / 3))  # Different starting positions

            # Calculate x and y positions (2D position in the orbit)
            x_offset = int(math.cos(angle) * radius)
            y_offset = int(math.sin(angle) * (radius * 0.5))  # Multiply by 0.5 to create elliptical orbits

            planet_x = center_x + x_offset
            planet_y = center_y + y_offset

            # Only place planet if it's within the grid bounds
            if 0 <= planet_x < width and 0 <= planet_y < height:
                # Check if we're not overwriting the sun
                if not (planet_x == center_x and planet_y == center_y):
                    grid[planet_y][planet_x] = f"{color}{planet}{RESET}"

            # Add orbit trail (faint dots showing the orbit path)
            if p < 4:  # Only for the first few planets to avoid clutter
                for trail_angle in range(0, 360, 15):  # Place a dot every 15 degrees
                    trail_rad = math.radians(trail_angle)
                    trail_x = center_x + int(math.cos(trail_rad) * radius)
                    trail_y = center_y + int(math.sin(trail_rad) * (radius * 0.5))

                    if (0 <= trail_x < width and 0 <= trail_y < height and
                        grid[trail_y][trail_x] == ' '):
                        grid[trail_y][trail_x] = f"\033[2;37m·{RESET}"  # Dim white dot

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (height + 2))

        # Print each line of the grid
        for row in grid:
            print(''.join(row))

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def sleep_matrix(duration=30):
    """
    Display a Matrix-style digital rain animation.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.1
    width = 60
    height = 7
    progress_width = 40

    # Matrix characters (digits and some special characters)
    matrix_chars = "01"

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    # Create space for animation
    for _ in range(height + 2):  # +2 for progress bar and time
        print()

    # Initialize rain drops
    drops = []
    for _ in range(width // 3):  # Create some initial drops
        drops.append({
            'x': random.randint(0, width - 1),
            'y': random.randint(0, height - 1),
            'speed': random.uniform(0.2, 1.0),
            'length': random.randint(1, 3),
            'progress': 0
        })

    for i in range(steps+1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Create matrix frame
        matrix = [[' ' for _ in range(width)] for _ in range(height)]

        # Update and render each drop
        for drop in drops:
            # Update drop position
            drop['progress'] += drop['speed']
            y_pos = int(drop['progress']) % (height * 2)

            # Only render if in visible area
            if y_pos < height:
                # Place the drop character
                matrix[y_pos][drop['x']] = f"{GREEN}{random.choice(matrix_chars)}{RESET}"

                # Add trail
                for trail in range(1, drop['length'] + 1):
                    trail_y = y_pos - trail
                    if 0 <= trail_y < height:
                        # Fade the trail (dimmer green)
                        fade = max(0, 1 - (trail / drop['length']))
                        fade_color = f"\033[38;5;{int(28 + fade * 8)}m"
                        matrix[trail_y][drop['x']] = f"{fade_color}{random.choice(matrix_chars)}{RESET}"

            # Randomly change some properties
            if random.random() < 0.01:
                drop['x'] = random.randint(0, width - 1)
                drop['length'] = random.randint(1, 3)
                drop['speed'] = random.uniform(0.2, 1.0)

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (height + 2))

        # Print each line of the matrix
        for row in matrix:
            print(''.join(row))

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def sleep_conway(duration=30):
    """
    Display Conway's Game of Life as a loading animation.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    interval = 0.5  # Update every 0.5 seconds
    width = 60
    height = 10

    # Colors
    ALIVE_CELL = f"{GREEN}█{RESET}"
    DEAD_CELL = f"{RESET}·{RESET}"

    # Initialize random grid (25% alive cells)
    grid = [[random.choice([0, 0, 0, 1]) for _ in range(width)] for _ in range(height)]

    # Frame generator function
    def generate_frame(step, progress):
        nonlocal grid

        # Render current grid
        grid_display = []
        for row in grid:
            line = []
            for cell in row:
                if cell:
                    line.append(ALIVE_CELL)
                else:
                    line.append(DEAD_CELL)
            grid_display.append(''.join(line))

        # Calculate next generation for the next frame
        steps = int(duration / interval)
        if step < steps:  # Don't calculate for the last step
            grid = next_generation(grid, width, height)

        # Note: progress parameter is not used in this animation
        return grid_display

    # Run the animation
    run_animation_loop(duration, interval, height, generate_frame)


def sleep_dna_helix(duration=30):
    """
    Display a rotating DNA double helix with animated base pairs.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.1
    width = 60
    height = 10
    progress_width = 40

    # DNA characters
    strand_char = "║"
    base_pairs = ["═", "─", "═", "─"]

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    # Create space for animation
    for _ in range(height + 2):
        print()

    for i in range(steps+1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Create DNA frame
        dna = [[' ' for _ in range(width)] for _ in range(height)]

        # Draw the double helix
        for y in range(height):
            # Calculate the x positions of the two strands using sine waves
            # with opposite phases to create the double helix effect
            x1 = int((width/2) + 10 * math.sin((i * 0.1) + (y * 0.5)))
            x2 = int((width/2) + 10 * math.sin((i * 0.1) + (y * 0.5) + math.pi))

            # Draw the strands
            if 0 <= x1 < width:
                dna[y][x1] = f"{BLUE}{strand_char}{RESET}"
            if 0 <= x2 < width:
                dna[y][x2] = f"{RED}{strand_char}{RESET}"

            # Draw base pairs connecting the strands
            if 0 <= x1 < width and 0 <= x2 < width:
                # Only draw base pairs at certain positions
                if y % 2 == 0:
                    base_pair_char = base_pairs[y % len(base_pairs)]
                    # Fill in the space between the strands with the base pair
                    start_x, end_x = min(x1, x2), max(x1, x2)
                    for x in range(start_x + 1, end_x):
                        dna[y][x] = f"{GREEN}{base_pair_char}{RESET}"

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (height + 2))

        # Print each line of the DNA
        for row in dna:
            print(''.join(row))

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def sleep_circuit_board(duration=30):
    """
    Display a circuit board with pulses of light traveling along paths.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.1
    width = 60
    height = 10
    progress_width = 40

    # Circuit characters
    h_line = "─"
    v_line = "│"
    corners = ["┌", "┐", "└", "┘"]
    junctions = ["┬", "┴", "├", "┤", "┼"]
    led = "●"

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    # Create space for animation
    for _ in range(height + 2):
        print()

    # Generate a static circuit board layout
    circuit = [[' ' for _ in range(width)] for _ in range(height)]

    # Create horizontal and vertical lines
    for y in range(height):
        for x in range(width):
            if random.random() < 0.3 and x % 3 == 0 and y % 2 == 0:
                # Create horizontal lines
                for i in range(min(random.randint(3, 8), width - x)):
                    circuit[y][x + i] = h_line

            if random.random() < 0.3 and y % 3 == 0 and x % 4 == 0:
                # Create vertical lines
                for i in range(min(random.randint(2, 4), height - y)):
                    circuit[y + i][x] = v_line

    # Add some junctions and corners
    for y in range(height):
        for x in range(width):
            if circuit[y][x] != ' ':
                if random.random() < 0.1:
                    circuit[y][x] = random.choice(junctions + corners)

    # Add some LEDs at endpoints or junctions
    leds = []
    for y in range(height):
        for x in range(width):
            if circuit[y][x] != ' ' and random.random() < 0.05:
                leds.append((x, y))

    # Create pulses that will travel along the circuit
    pulses = []
    for _ in range(5):
        # Start pulses at random positions on the circuit
        start_y = random.randint(0, height - 1)
        start_x = random.randint(0, width - 1)

        # Only start pulses on circuit elements
        if circuit[start_y][start_x] != ' ':
            pulses.append({
                'x': start_x,
                'y': start_y,
                'dx': random.choice([-1, 0, 1]),
                'dy': random.choice([-1, 0, 1]),
                'color': random.choice([CYAN, GREEN, YELLOW, BLUE]),
                'life': random.randint(10, 30)
            })

    for i in range(steps+1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Create frame from the static circuit
        frame = [row[:] for row in circuit]

        # Update and draw pulses
        new_pulses = []
        for pulse in pulses:
            # Draw the pulse
            if 0 <= pulse['x'] < width and 0 <= pulse['y'] < height:
                if circuit[pulse['y']][pulse['x']] != ' ':
                    frame[pulse['y']][pulse['x']] = f"{pulse['color']}{circuit[pulse['y']][pulse['x']]}{RESET}"

            # Move the pulse
            pulse['x'] += pulse['dx']
            pulse['y'] += pulse['dy']
            pulse['life'] -= 1

            # Keep pulse if it's still alive and in bounds
            if pulse['life'] > 0 and 0 <= pulse['x'] < width and 0 <= pulse['y'] < height:
                new_pulses.append(pulse)
            elif random.random() < 0.3:
                # Randomly create new pulses
                start_y = random.randint(0, height - 1)
                start_x = random.randint(0, width - 1)
                if circuit[start_y][start_x] != ' ':
                    new_pulses.append({
                        'x': start_x,
                        'y': start_y,
                        'dx': random.choice([-1, 0, 1]),
                        'dy': random.choice([-1, 0, 1]),
                        'color': random.choice([CYAN, GREEN, YELLOW, BLUE]),
                        'life': random.randint(10, 30)
                    })

        pulses = new_pulses

        # Blink LEDs
        for led_x, led_y in leds:
            if random.random() < 0.1:  # 10% chance to blink
                led_color = random.choice([RED, GREEN, YELLOW, BLUE])
                frame[led_y][led_x] = f"{led_color}{led}{RESET}"

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (height + 2))

        # Print each line of the circuit
        for row in frame:
            print(''.join(row))

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def sleep_weather_system(duration=30):
    """
    Display an animated weather system with clouds, rain, and lightning.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.1
    width = 60
    height = 10
    progress_width = 40

    # Weather elements
    cloud_chars = ["░", "▒", "▓"]
    rain_char = "│"
    lightning_chars = ["╲", "╱", "│"]
    sun_char = "●"

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    # Create space for animation
    for _ in range(height + 2):
        print()

    # Initialize clouds
    clouds = []
    for _ in range(3):
        cloud_width = random.randint(8, 15)
        clouds.append({
            'x': random.randint(-cloud_width, width),
            'y': random.randint(0, height // 3),
            'width': cloud_width,
            'height': random.randint(2, 3),
            'speed': random.uniform(0.1, 0.3),
            'density': random.uniform(0.6, 0.9)
        })

    # Weather state (cycles through different weather patterns)
    weather_states = ["sunny", "cloudy", "rainy", "stormy"]
    weather_state = random.choice(weather_states)
    weather_timer = 0
    weather_duration = random.randint(20, 40)

    # Rain drops
    raindrops = []

    # Lightning
    lightning = None
    lightning_timer = 0

    for i in range(steps+1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Update weather state
        weather_timer += 1
        if weather_timer >= weather_duration:
            weather_timer = 0
            weather_duration = random.randint(20, 40)
            # Transition to a new weather state
            current_idx = weather_states.index(weather_state)
            # Either move to next state or randomly choose
            if random.random() < 0.7:
                weather_state = weather_states[(current_idx + 1) % len(weather_states)]
            else:
                weather_state = random.choice(weather_states)

        # Create weather frame
        weather = [[' ' for _ in range(width)] for _ in range(height)]

        # Draw sun if sunny or partly cloudy
        if weather_state in ["sunny", "cloudy"]:
            sun_x = width - 10
            sun_y = 2
            weather[sun_y][sun_x] = f"{YELLOW}{sun_char}{RESET}"

            # Add sun rays
            for angle in range(0, 360, 45):
                rad = math.radians(angle)
                ray_x = int(sun_x + 2 * math.cos(rad))
                ray_y = int(sun_y + math.sin(rad))
                if 0 <= ray_x < width and 0 <= ray_y < height:
                    weather[ray_y][ray_x] = f"{YELLOW}*{RESET}"

        # Update and draw clouds
        for cloud in clouds:
            # Move cloud
            cloud['x'] += cloud['speed']
            if cloud['x'] > width:
                # Reset cloud position when it moves off screen
                cloud['x'] = -cloud['width']
                cloud['y'] = random.randint(0, height // 3)

            # Draw cloud
            for cy in range(cloud['height']):
                for cx in range(cloud['width']):
                    x = int(cloud['x']) + cx
                    y = int(cloud['y']) + cy
                    if 0 <= x < width and 0 <= y < height:
                        if random.random() < cloud['density']:
                            cloud_color = RESET if weather_state in ["sunny", "cloudy"] else BLUE
                            weather[y][x] = f"{cloud_color}{random.choice(cloud_chars)}{RESET}"

        # Handle rain in rainy or stormy weather
        if weather_state in ["rainy", "stormy"]:
            # Create new raindrops
            if random.random() < 0.3:
                for _ in range(random.randint(1, 3)):
                    raindrops.append({
                        'x': random.randint(0, width - 1),
                        'y': random.randint(0, height // 3),
                        'speed': random.uniform(0.5, 1.0),
                        'length': random.randint(1, 3)
                    })

            # Update and draw raindrops
            new_raindrops = []
            for drop in raindrops:
                # Move drop
                drop['y'] += drop['speed']

                # Draw drop and its trail
                for l in range(drop['length']):
                    y = int(drop['y']) - l
                    x = int(drop['x'])
                    if 0 <= x < width and 0 <= y < height:
                        weather[y][x] = f"{BLUE}{rain_char}{RESET}"

                # Keep drop if it's still on screen
                if drop['y'] - drop['length'] < height:
                    new_raindrops.append(drop)

            raindrops = new_raindrops

        # Handle lightning in stormy weather
        if weather_state == "stormy":
            if lightning is None and random.random() < 0.1:
                # Create new lightning bolt
                lightning = {
                    'x': random.randint(width // 4, 3 * width // 4),
                    'y': 0,
                    'segments': random.randint(3, 6),
                    'direction': random.choice([-1, 1])
                }
                lightning_timer = 0

            if lightning is not None:
                lightning_timer += 1

                # Draw lightning
                x, y = lightning['x'], lightning['y']
                for s in range(lightning['segments']):
                    # Zigzag pattern
                    x += lightning['direction']
                    y += 1
                    lightning['direction'] *= -1

                    if 0 <= x < width and 0 <= y < height:
                        weather[y][x] = f"{YELLOW}{random.choice(lightning_chars)}{RESET}"

                # Lightning disappears after a short time
                if lightning_timer > 3:
                    lightning = None

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (height + 2))

        # Print each line of the weather
        for row in weather:
            print(''.join(row))

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def next_generation(grid, width, height):
    """Calculate the next generation for Conway's Game of Life."""
    # Create a new grid
    new_grid = [[0 for _ in range(width)] for _ in range(height)]

    # Apply Conway's Game of Life rules
    for y in range(height):
        for x in range(width):
            # Count live neighbors (including wrapping around edges)
            neighbors = 0
            for dy in [-1, 0, 1]:
                for dx in [-1, 0, 1]:
                    if dx == 0 and dy == 0:
                        continue
                    nx, ny = (x + dx) % width, (y + dy) % height
                    neighbors += grid[ny][nx]

            # Apply rules
            if grid[y][x]:
                # Cell is alive
                if neighbors < 2 or neighbors > 3:
                    # Dies from underpopulation or overpopulation
                    new_grid[y][x] = 0
                else:
                    # Survives
                    new_grid[y][x] = 1
            else:
                # Cell is dead
                if neighbors == 3:
                    # Becomes alive from reproduction
                    new_grid[y][x] = 1

    return new_grid


def sleep_bouncing_logo(duration=30):
    """
    Display an application logo bouncing around the screen like the classic DVD screensaver.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.05
    width = 60
    height = 10
    progress_width = 40

    # Logo configuration
    logo_text = "AMPERE"
    logo_width = len(logo_text)
    logo_height = 1

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    # Create space for animation
    for _ in range(height + 2):
        print()

    # Initialize logo position and velocity
    logo_x = random.randint(0, width - logo_width)
    logo_y = random.randint(0, height - logo_height)
    dx = random.choice([-1, 1]) * 0.5
    dy = random.choice([-1, 1]) * 0.3

    # Logo color state
    current_color = random.choice([RED, GREEN, BLUE, YELLOW, MAGENTA, CYAN, PURPLE])

    for i in range(steps+1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Update logo position
        logo_x += dx
        logo_y += dy

        # Bounce off walls and change color
        if logo_x <= 0 or logo_x >= width - logo_width:
            dx = -dx
            current_color = random.choice([RED, GREEN, BLUE, YELLOW, MAGENTA, CYAN, PURPLE])
        if logo_y <= 0 or logo_y >= height - logo_height:
            dy = -dy
            current_color = random.choice([RED, GREEN, BLUE, YELLOW, MAGENTA, CYAN, PURPLE])

        # Create frame
        frame = [[' ' for _ in range(width)] for _ in range(height)]

        # Draw logo
        logo_x_int = int(logo_x)
        logo_y_int = int(logo_y)

        if 0 <= logo_y_int < height:
            for x, char in enumerate(logo_text):
                if 0 <= logo_x_int + x < width:
                    frame[logo_y_int][logo_x_int + x] = f"{current_color}{char}{RESET}"

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (height + 2))

        # Print each line of the frame
        for row in frame:
            print(''.join(row))

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def sleep_binary_clock(duration=30):
    """
    Display an animated binary clock that shows the actual time.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    interval = 0.5  # Update every half second
    width = 60
    height = 10

    # Binary clock characters
    on_bit = "●"
    off_bit = "○"

    # Frame generator function
    def generate_frame(step, progress):
        # Note: step and progress parameters are not directly used in this animation
        # but we use the current time instead

        # Get current time
        current_time = datetime.now()
        hours = current_time.hour
        minutes = current_time.minute
        seconds = current_time.second

        # Create binary clock frame
        clock = [[' ' for _ in range(width)] for _ in range(height)]

        # Draw binary clock
        # Format: Hours (0-23) | Minutes (0-59) | Seconds (0-59)

        # Column labels
        labels = ["H", "M", "S"]
        for idx, label in enumerate(labels):
            clock[1][10 + idx * 10] = label

        # Row labels (bit values)
        # For hours (0-23): 16, 8, 4, 2, 1
        # For minutes/seconds (0-59): 32, 16, 8, 4, 2, 1
        hour_bit_values = [16, 8, 4, 2, 1]
        min_sec_bit_values = [32, 16, 8, 4, 2, 1]

        # Display bit values for hours
        for idx, value in enumerate(hour_bit_values):
            if idx == 0:
                clock[2 + idx][4] = str(value)
            elif idx < len(hour_bit_values):
                clock[2 + idx][5] = str(value)

        # Display bit values for minutes and seconds
        for idx, value in enumerate(min_sec_bit_values):
            if idx == 0:
                clock[2 + idx][13] = str(value)
                clock[2 + idx][23] = str(value)
            elif idx == 1:
                clock[2 + idx][14] = str(value)
                clock[2 + idx][24] = str(value)
            elif idx < len(min_sec_bit_values):
                clock[2 + idx][15] = str(value)
                clock[2 + idx][25] = str(value)

        # Draw hours in binary (5 bits needed for 0-23)
        hours_bits = format(hours, '05b')
        for bit_idx, bit in enumerate(hours_bits):
            x = 10
            if bit_idx == 0:
                x = 9
            y = 2 + bit_idx
            if bit == '1':
                clock[y][x] = f"{RED}{on_bit}{RESET}"
            else:
                clock[y][x] = f"{RESET}{off_bit}{RESET}"

        # Draw minutes in binary (6 bits needed for 0-59)
        minutes_bits = format(minutes, '06b')
        for bit_idx, bit in enumerate(minutes_bits):
            x = 20
            if bit_idx == 0:
                x = 18
            elif bit_idx == 1:
                x = 19
            y = 2 + bit_idx
            if bit == '1':
                clock[y][x] = f"{GREEN}{on_bit}{RESET}"
            else:
                clock[y][x] = f"{RESET}{off_bit}{RESET}"

        # Draw seconds in binary (6 bits needed for 0-59)
        seconds_bits = format(seconds, '06b')
        for bit_idx, bit in enumerate(seconds_bits):
            x = 30
            if bit_idx == 0:
                x = 27
            elif bit_idx == 1:
                x = 28
            y = 2 + bit_idx
            if bit == '1':
                clock[y][x] = f"{BLUE}{on_bit}{RESET}"
            else:
                clock[y][x] = f"{RESET}{off_bit}{RESET}"

        # Draw digital time at the bottom
        digital_time = current_time.strftime("%H:%M:%S")
        time_x = (width - len(digital_time)) // 2
        for idx, char in enumerate(digital_time):
            if 0 <= time_x + idx < width:
                clock[8][time_x + idx] = f"{YELLOW}{char}{RESET}"

        return clock

    # Run the animation
    run_animation_loop(duration, interval, height, generate_frame)


def sleep_memory_blocks(duration=30):
    """
    Display blocks of memory (█ ▓ ▒ ░) that reorganize over time from chaos to order.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.5  # Update every 0.5 seconds
    width = 60
    height = 10
    progress_width = 40

    # Memory block characters
    block_chars = ["█", "▓", "▒", "░"]

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    # Create space for animation
    for _ in range(height + 2):
        print()

    # Initialize memory blocks in a chaotic state
    memory = [[random.choice(block_chars) for _ in range(width)] for _ in range(height)]

    # Target pattern (organized memory)
    target = [[block_chars[min(3, (x // 5) % 4)] for x in range(width)] for _ in range(height)]

    for i in range(steps+1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Gradually organize the memory
        organization_level = i / steps  # 0.0 to 1.0

        # Create frame
        frame = [[' ' for _ in range(width)] for _ in range(height)]

        for y in range(height):
            for x in range(width):
                # Determine if this cell should be organized yet
                if random.random() < organization_level:
                    # Use the target (organized) value
                    char = target[y][x]
                else:
                    # Use the chaotic value
                    char = memory[y][x]

                    # Occasionally update chaotic cells
                    if random.random() < 0.1:
                        memory[y][x] = random.choice(block_chars)

                # Assign color based on block type
                if char == "█":
                    color = BLUE
                elif char == "▓":
                    color = CYAN
                elif char == "▒":
                    color = TEAL
                else:  # "░"
                    color = GREEN

                frame[y][x] = f"{color}{char}{RESET}"

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (height + 2))

        # Print each line of the frame
        for row in frame:
            print(''.join(row))

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def sleep_ascii_inkwell(duration=30):
    """
    Display letters dripping from an invisible pen, forming incomplete words or poetry
    that smudge or bleed away.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    total_seconds = duration
    interval = 0.1
    width = 60
    height = 10
    progress_width = 40

    # Poetic fragments that will drip
    fragments = [
        "whispers in code",
        "digital dreams",
        "memory fades",
        "time suspends",
        "waiting patiently",
        "electrons dance",
        "bits in motion",
        "data flows like ink",
        "processing thoughts",
        "silicon poetry"
    ]

    # Calculate steps
    steps = int(total_seconds / interval)

    # Print initial message
    print(f"Pausing for {format_time_display(total_seconds)}... Starting at {datetime.now().strftime('%H:%M:%S')}")
    end_time = datetime.now() + timedelta(seconds=total_seconds)
    print(f"Will resume at {end_time.strftime('%H:%M:%S')}\n")

    # Create space for animation
    for _ in range(height + 2):
        print()

    # Initialize drips
    drips = []
    active_fragment = random.choice(fragments)
    fragment_pos = 0
    next_drip = 0

    for i in range(steps+1):
        # Calculate progress
        progress = min(i / steps, 1.0)
        elapsed = i * interval
        remaining = total_seconds - elapsed

        # Create frame
        frame = [[' ' for _ in range(width)] for _ in range(height)]

        # Add new drips
        next_drip -= 1
        if next_drip <= 0 and fragment_pos < len(active_fragment):
            drips.append({
                'char': active_fragment[fragment_pos],
                'x': width // 2 - len(active_fragment) // 2 + fragment_pos,
                'y': 0,
                'speed': random.uniform(0.2, 0.5),
                'age': 0,
                'max_age': random.randint(20, 40)
            })
            fragment_pos += 1
            next_drip = random.randint(2, 5)

            # Start a new fragment if this one is complete
            if fragment_pos >= len(active_fragment):
                active_fragment = random.choice(fragments)
                fragment_pos = 0
                next_drip = random.randint(10, 20)  # Pause between fragments

        # Update and draw drips
        new_drips = []
        for drip in drips:
            # Update position
            drip['y'] += drip['speed']
            drip['age'] += 1

            # Draw the drip
            y_pos = int(drip['y'])
            if 0 <= y_pos < height and 0 <= drip['x'] < width:
                # Calculate fade based on age
                fade_factor = min(1.0, drip['age'] / drip['max_age'])

                if fade_factor < 0.3:
                    color = BLUE
                elif fade_factor < 0.6:
                    color = CYAN
                elif fade_factor < 0.9:
                    color = TEAL
                else:
                    color = RESET

                # Add smudge/bleed effect as the character ages
                if random.random() < fade_factor * 0.5:
                    char = random.choice(".,;:·")
                else:
                    char = drip['char']

                frame[y_pos][drip['x']] = f"{color}{char}{RESET}"

                # Add drip trail
                for trail in range(1, min(3, y_pos)):
                    if random.random() < 0.7 - (trail * 0.2):
                        trail_char = "'"
                        frame[y_pos - trail][drip['x']] = f"\033[2;36m{trail_char}{RESET}"

            # Keep drip if it's still active
            if drip['age'] < drip['max_age'] and drip['y'] < height:
                new_drips.append(drip)

        drips = new_drips

        # Create progress bar
        filled_width = int(progress_width * progress)
        progress_bar = f"[{CYAN}{'█' * filled_width}{RESET}{'░' * (progress_width - filled_width)}]"

        # Format time display
        time_display = format_time_display(remaining) + " remaining"
        percentage = f"{progress * 100:.1f}%"

        # Move cursor up to overwrite previous frame
        sys.stdout.write('\033[F' * (height + 2))

        # Print each line of the frame
        for row in frame:
            print(''.join(row))

        # Print progress bar and time
        print(f"{progress_bar} {percentage} ({time_display})")
        print(f"Elapsed: {int(elapsed)}s / {total_seconds}s")

        # Sleep for the interval
        time.sleep(interval)


def sleep_neural_network(duration=30):
    """
    Display a brain-like neural network with nodes and connections that fire signals.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    interval = 0.1
    width = 60
    height = 10

    # Neural network elements
    node_char = "●"
    active_node_char = "◉"

    # Generate neural network layout
    # Create nodes at random positions
    nodes = []
    for _ in range(15):
        nodes.append({
            'x': random.randint(5, width - 5),
            'y': random.randint(1, height - 2),
            'active': False,
            'activation_time': 0,
            'connections': []
        })

    # Create connections between nodes
    for node in nodes:
        # Connect to 2-4 other nodes
        connection_count = random.randint(2, 4)
        possible_connections = nodes.copy()
        possible_connections.remove(node)

        for _ in range(min(connection_count, len(possible_connections))):
            if not possible_connections:
                break

            target = random.choice(possible_connections)
            possible_connections.remove(target)

            # Calculate connection path
            dx = target['x'] - node['x']
            dy = target['y'] - node['y']
            distance = max(1, abs(dx) + abs(dy))

            node['connections'].append({
                'target': target,
                'path': [(node['x'] + int(dx * i / distance),
                          node['y'] + int(dy * i / distance))
                         for i in range(1, distance)],
                'active': False,
                'signal_pos': 0
            })

    # Initialize some random activations
    for _ in range(3):
        random.choice(nodes)['active'] = True

    # Frame generator function
    def generate_frame(step, progress):
        # Note: step and progress parameters are not used in this animation
        # Create frame
        frame = [[' ' for _ in range(width)] for _ in range(height)]

        # Update neural network state
        for node in nodes:
            # Update node activation
            if node['active']:
                node['activation_time'] += 1

                # Deactivate after a while
                if node['activation_time'] > 5:
                    node['active'] = False
                    node['activation_time'] = 0

                    # Activate connected nodes
                    for conn in node['connections']:
                        conn['active'] = True
                        conn['signal_pos'] = 0

            # Draw node
            if 0 <= node['x'] < width and 0 <= node['y'] < height:
                if node['active']:
                    color = random.choice([RED, YELLOW, CYAN, GREEN])
                    frame[node['y']][node['x']] = f"{color}{active_node_char}{RESET}"
                else:
                    frame[node['y']][node['x']] = f"{BLUE}{node_char}{RESET}"

            # Update and draw connections
            for conn in node['connections']:
                # Draw connection path
                for x, y in conn['path']:
                    if 0 <= x < width and 0 <= y < height:
                        # Determine connection character based on direction
                        if x == node['x']:
                            char = "│"
                        elif y == node['y']:
                            char = "─"
                        elif (x < node['x'] and y < node['y']) or (x > node['x'] and y > node['y']):
                            char = "╲"
                        else:
                            char = "╱"

                        # Normal connection
                        if not conn['active'] or conn['signal_pos'] >= len(conn['path']):
                            frame[y][x] = f"\033[2;36m{char}{RESET}"  # Dim cyan
                        # Active signal traveling along connection
                        elif conn['path'].index((x, y)) == conn['signal_pos']:
                            frame[y][x] = f"{YELLOW}{char}{RESET}"

                # Update signal position
                if conn['active']:
                    conn['signal_pos'] += 1

                    # When signal reaches target, activate it
                    if conn['signal_pos'] >= len(conn['path']):
                        conn['active'] = False
                        conn['target']['active'] = True
                        conn['target']['activation_time'] = 0

        # Randomly activate a node occasionally
        if random.random() < 0.05:
            random.choice(nodes)['active'] = True

        return frame

    # Run the animation
    run_animation_loop(duration, interval, height, generate_frame)


def sleep_ant_worker(duration=30):
    """
    Display an ant (●) that drags blocks from one side of the window to the other.

    Args:
        duration: Duration in seconds to run the animation
    """
    # Configuration
    interval = 0.05  # Update every 0.2 seconds
    width = 60
    height = 10

    # Ant and block characters
    ant_char = "●"
    block_chars = ["█", "▓", "▒", "░"]
    path_char = "·"

    # Source and target piles
    source_x = 5
    target_x = width - 5

    # Create a path for the ant to follow (a simple curve)
    path = []
    # Start at source pile
    path_start_y = height - 2  # Just above ground level

    # Generate a curved path from source to target
    for i in range(source_x, target_x + 1):
        # Create a gentle curve using sine function
        curve_height = int(math.sin((i - source_x) / (target_x - source_x) * math.pi) * 3)
        path.append((i, path_start_y - curve_height))

    # Frame generator function
    def generate_frame(step, progress):
        # Create frame
        frame = [[' ' for _ in range(width)] for _ in range(height)]

        # Calculate total blocks and current animation state
        total_blocks = 20

        # Track which blocks have been picked up and delivered
        # We'll use the step to determine the current state of the animation
        path_length = len(path)
        cycle_length = path_length  # One full trip
        current_cycle = step // cycle_length
        position_in_cycle = step % cycle_length

        # Calculate how many blocks have been fully moved (completed cycles)
        completed_blocks = min(current_cycle, total_blocks)

        # Determine if we're currently carrying a block
        carrying_block = position_in_cycle > 0 and position_in_cycle < path_length - 1 and completed_blocks < total_blocks

        # Determine if we've picked up a block but not yet delivered it
        block_picked_up = position_in_cycle > 0 and completed_blocks < total_blocks

        # Calculate remaining blocks in source pile
        source_blocks = total_blocks - completed_blocks
        if block_picked_up:
            source_blocks -= 1  # One block is being carried

        # Calculate blocks in target pile
        target_blocks = completed_blocks

        # Draw source pile
        for i in range(min(source_blocks, height)):
            y = height - 1 - i
            block_type = i % len(block_chars)
            frame[y][source_x] = f"{BLUE}{block_chars[block_type]}{RESET}"

        # Draw target pile
        for i in range(min(target_blocks, height)):
            y = height - 1 - i
            block_type = i % len(block_chars)
            frame[y][target_x] = f"{GREEN}{block_chars[block_type]}{RESET}"

        # Draw the path
        for x, y in path:
            if frame[y][x] == ' ':  # Don't overwrite blocks or ant
                frame[y][x] = f"{RESET}{path_char}{RESET}"

        # Determine ant position and state
        if completed_blocks < total_blocks:
            # Calculate ant position along the path
            ant_x, ant_y = path[position_in_cycle]

            # Draw ant
            frame[ant_y][ant_x] = f"{ORANGE}{ant_char}{RESET}"

            # If carrying a block, show it above the ant
            if carrying_block:
                # Determine which block is being carried
                block_type = completed_blocks % len(block_chars)
                block_char = block_chars[block_type]

                # Show block being carried (slightly above ant)
                if ant_y > 0:
                    frame[ant_y-1][ant_x] = f"{CYAN}{block_char}{RESET}"

            # Draw a small trail behind the ant
            trail_idx = max(0, position_in_cycle - 1)
            if trail_idx < len(path):
                trail_x, trail_y = path[trail_idx]
                if frame[trail_y][trail_x] == f"{RESET}{path_char}{RESET}":  # Only overwrite path
                    frame[trail_y][trail_x] = f"{RESET}·{RESET}"
        else:
            # All blocks moved, ant rests on top of the target pile
            ant_y = max(0, height - target_blocks)
            frame[ant_y][target_x] = f"{ORANGE}{ant_char}{RESET}"

        # Draw ground
        for x in range(width):
            frame[height-1][x] = f"{RESET}_{RESET}" if frame[height-1][x] == ' ' else frame[height-1][x]

        return frame

    # Run the animation
    run_animation_loop(duration, interval, height, generate_frame)


def random_display_sleep(duration=30):
    """
    Display a random loading animation.

    Args:
        duration: Duration in seconds to run the animation
    """
    displays = [
        sleep_wave,
        # sleep_orbit,
        # sleep_matrix,
        sleep_conway,
        # sleep_dna_helix,
        # sleep_circuit_board,
        sleep_weather_system,
        # sleep_bouncing_logo,
        sleep_binary_clock,
        # sleep_memory_blocks,
        # sleep_ascii_inkwell,
        # sleep_neural_network,
        # sleep_ant_worker,
    ]
    random.choice(displays)(duration)

def menu(duration=30):
    """
    Display a menu of available animations and let the user select one.

    Args:
        duration: Duration in seconds to run the selected animation
    """
    displays = [
        sleep_wave,
        sleep_orbit,
        sleep_matrix,
        sleep_conway,
        sleep_dna_helix,
        sleep_circuit_board,
        sleep_weather_system,
        sleep_bouncing_logo,
        sleep_binary_clock,
        sleep_memory_blocks,
        sleep_ascii_inkwell,
        sleep_neural_network,
        sleep_ant_worker,
    ]
    for i, display in enumerate(displays, start=1):
        print(f"{i}. {display.__name__}")
    selection = input(f"Select a display (1-{len(displays)}): ")
    try:
        index = int(selection) - 1
        if 0 <= index < len(displays):
            displays[index](duration)
        else:
            print(f"Invalid selection. Please enter a number between 1 and {len(displays)}.")
            menu(duration)
    except ValueError:
        print("Invalid input. Please enter a number.")
        menu(duration)

if __name__ == "__main__":
    # random_display_sleep(10)

    menu(60)
