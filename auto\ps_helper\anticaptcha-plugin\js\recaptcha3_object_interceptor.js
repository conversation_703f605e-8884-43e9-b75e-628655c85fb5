(function(){var e="_recaptchaOnloadMethod";if(e){window[e]=function(){var r;if(grecaptcha){grecaptcha.execute=function(a,n,i){return new Promise((e,t)=>{n.isEnterprise=i;setTimeout(function(){window.postMessage({receiver:"recaptchaContentScript",type:"solveRecaptchaV3",siteKey:a,params:n},window.location.href)},1e3);r=e})};if(typeof grecaptcha.enterprise!=="undefined"){grecaptcha.enterprise.execute=function(){var e=Array.from(arguments);e.push(true);return grecaptcha.execute.apply(this,e)}}}window.addEventListener("message",function(e){if(!e.data||typeof e.data.receiver=="undefined"||e.data.receiver!="recaptchaObjectInterceptor"){return}var t=e.data;if(t.type==="recaptchaTaskSolution"){taskSolution=e.data.taskSolution;if(typeof r==="function"){r(taskSolution)}}});if(typeof lastOriginalOnloadMethodName!=="undefined"&&lastOriginalOnloadMethodName&&typeof window[lastOriginalOnloadMethodName]==="function"){window[lastOriginalOnloadMethodName].apply(this,arguments)}return}}})();