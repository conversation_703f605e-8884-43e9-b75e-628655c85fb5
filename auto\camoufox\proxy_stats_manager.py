"""
Module for tracking proxy connection statistics.

This module provides functionality to track the success and failure rates
of proxies used in the application.
"""

import os
import csv
from typing import Dict, Optional, List, Tuple
import re

# Path to the CSV file storing proxy statistics
PROXY_STATS_FILE = os.path.join(os.path.dirname(__file__), "proxy_stats.csv")

def extract_proxy_address(proxy_server: str) -> str:
    """
    Extract the proxy address from the proxy server string.
    
    Args:
        proxy_server: The proxy server string (e.g., "http://*************:4444")
        
    Returns:
        The proxy address (e.g., "*************:4444")
    """
    # Extract the address part from the proxy server string
    match = re.search(r'http://([^/]+)', proxy_server)
    if match:
        return match.group(1)
    return proxy_server  # Return as is if pattern doesn't match


def load_proxy_stats() -> Dict[str, Dict[str, int]]:
    """
    Load proxy statistics from the CSV file.
    
    Returns:
        Dictionary mapping proxy addresses to their success and failure counts
    """
    stats = {}
    
    # Create the file if it doesn't exist
    if not os.path.exists(PROXY_STATS_FILE):
        with open(PROXY_STATS_FILE, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['proxy_address', 'successful_connections', 'failed_connections'])
        return stats
    
    # Read the existing stats
    with open(PROXY_STATS_FILE, 'r', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            stats[row['proxy_address']] = {
                'successful_connections': int(row['successful_connections']),
                'failed_connections': int(row['failed_connections'])
            }
    
    return stats


def save_proxy_stats(stats: Dict[str, Dict[str, int]]) -> None:
    """
    Save proxy statistics to the CSV file.
    
    Args:
        stats: Dictionary mapping proxy addresses to their success and failure counts
    """
    with open(PROXY_STATS_FILE, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['proxy_address', 'successful_connections', 'failed_connections'])
        for proxy_address, counts in stats.items():
            writer.writerow([
                proxy_address,
                counts['successful_connections'],
                counts['failed_connections']
            ])


def update_proxy_stats(proxy_server: Optional[Dict[str, str]], success: bool) -> None:
    """
    Update the statistics for a proxy.
    
    Args:
        proxy_server: The proxy server dictionary with 'server' key
        success: Whether the connection was successful
    """
    if not proxy_server or 'server' not in proxy_server:
        return
    
    # Extract the proxy address
    proxy_address = extract_proxy_address(proxy_server['server'])
    
    # Load existing stats
    stats = load_proxy_stats()
    
    # Initialize stats for this proxy if it doesn't exist
    if proxy_address not in stats:
        stats[proxy_address] = {
            'successful_connections': 0,
            'failed_connections': 0
        }
    
    # Update the appropriate counter
    if success:
        stats[proxy_address]['successful_connections'] += 1
    else:
        stats[proxy_address]['failed_connections'] += 1
    
    # Save the updated stats
    save_proxy_stats(stats)
    
    # Print the current stats for this proxy
    if success:
        print(f"Proxy {proxy_address} connection successful. "
              f"Success rate: {stats[proxy_address]['successful_connections']} successes, "
              f"{stats[proxy_address]['failed_connections']} failures")
    else:
        print(f"Proxy {proxy_address} connection failed. "
              f"Success rate: {stats[proxy_address]['successful_connections']} successes, "
              f"{stats[proxy_address]['failed_connections']} failures")


def get_proxy_stats(proxy_server: Optional[Dict[str, str]]) -> Tuple[int, int]:
    """
    Get the success and failure counts for a proxy.
    
    Args:
        proxy_server: The proxy server dictionary with 'server' key
        
    Returns:
        Tuple of (successful_connections, failed_connections)
    """
    if not proxy_server or 'server' not in proxy_server:
        return 0, 0
    
    # Extract the proxy address
    proxy_address = extract_proxy_address(proxy_server['server'])
    
    # Load existing stats
    stats = load_proxy_stats()
    
    # Return the stats for this proxy, or (0, 0) if it doesn't exist
    if proxy_address in stats:
        return (
            stats[proxy_address]['successful_connections'],
            stats[proxy_address]['failed_connections']
        )
    return 0, 0


def get_all_proxy_stats() -> List[Dict[str, any]]:
    """
    Get statistics for all proxies.
    
    Returns:
        List of dictionaries with proxy addresses and their stats
    """
    stats = load_proxy_stats()
    return [
        {
            'proxy_address': proxy_address,
            'successful_connections': counts['successful_connections'],
            'failed_connections': counts['failed_connections'],
            'total_connections': counts['successful_connections'] + counts['failed_connections'],
            'success_rate': (counts['successful_connections'] / 
                            (counts['successful_connections'] + counts['failed_connections'])) * 100
                            if (counts['successful_connections'] + counts['failed_connections']) > 0 else 0
        }
        for proxy_address, counts in stats.items()
    ]
