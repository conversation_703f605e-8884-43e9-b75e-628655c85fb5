"""PlayStation account creation automation using CamouFox.

This module provides functionality to automate PlayStation account creation
using CamouFox with identity generation, email verification, and captcha handling.
"""

import os
import json
import time
from enum import Enum
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime
import pytz
import query_builder as qb
from camoufox import Camoufox, launch_options
from auto.camoufox.mail import get_email_verification_link, get_email_verification_code
from auto.camoufox.proxy_stats_manager import update_proxy_stats
from auto.camoufox.proxy_manager import get_proxy
from auto.camoufox.account_query_manager import update_sso_code
from auto.ps_helper.create_identity import IdentityGen
from auto.ps_helper.mail_util import add_user
from auto.ps_helper.postgres import PostgrestClient
import sys
import asyncio


# Selectors for PlayStation account creation
class CreationSelectors:
    # Main navigation
    SIGN_IN_BUTTON = "button[data-qa='web-toolbar#signin-button']"
    CREATE_ACCOUNT_LINK = "a[href*='create-account']"
    
    # Registration form fields
    EMAIL_INPUT = "input[name='email']"
    PASSWORD_INPUT = "input[name='password']"
    CONFIRM_PASSWORD_INPUT = "input[name='confirmPassword']"
    FIRST_NAME_INPUT = "input[name='firstName']"
    LAST_NAME_INPUT = "input[name='lastName']"
    
    # Date of birth fields
    DOB_MONTH_SELECT = "select[name='dobMonth']"
    DOB_DAY_SELECT = "select[name='dobDay']"
    DOB_YEAR_SELECT = "select[name='dobYear']"
    
    # Location fields
    COUNTRY_SELECT = "select[name='country']"
    STATE_SELECT = "select[name='state']"
    CITY_INPUT = "input[name='city']"
    POSTAL_CODE_INPUT = "input[name='postalCode']"
    
    # Terms and conditions
    TERMS_CHECKBOX = "input[name='termsOfService']"
    PRIVACY_CHECKBOX = "input[name='privacyPolicy']"
    
    # Submit button
    CREATE_ACCOUNT_BUTTON = "button[type='submit']"
    
    # Verification
    VERIFICATION_CODE_INPUT = "input[name='verificationCode']"
    VERIFY_BUTTON = "button[data-qa='verify-button']"
    
    # Captcha
    CAPTCHA_IFRAME = "iframe[src*='captcha']"
    CAPTCHA_CHECKBOX = "input[type='checkbox']"
    
    # Error messages
    ERROR_MESSAGE = ".error-message"
    FIELD_ERROR = ".field-error"


class CreationPageState(str, Enum):
    UNKNOWN = "unknown"
    ON_HOME_PAGE = "on_home_page"
    CAN_CLICK_SIGN_IN = "can_click_sign_in"
    CAN_CLICK_CREATE_ACCOUNT = "can_click_create_account"
    ON_REGISTRATION_FORM = "on_registration_form"
    CAPTCHA_REQUIRED = "captcha_required"
    EMAIL_VERIFICATION_REQUIRED = "email_verification_required"
    ACCOUNT_CREATED = "account_created"
    ERROR_PAGE = "error_page"


def opts_path(name: str) -> str:
    return f"auto/camoufox/profiles/{name}/opts.json"


def config_exists(name: str) -> bool:
    return Path(opts_path(name)).exists()


def generate_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Generate a new profile configuration for account creation.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        New profile configuration dictionary
    """
    profile_dir = os.path.dirname(opts_path(name))

    try:
        opts = launch_options(
            user_data_dir=profile_dir,
            os=['windows'],
            proxy=proxy,
            geoip=True,
            window=(1080, 720),
            humanize=True,
            headless=False
        )

        if proxy:
            update_proxy_stats(proxy, True)

        os.makedirs(profile_dir, exist_ok=True)
        with open(opts_path(name), "w") as opts_file:
            json.dump(opts, opts_file, indent=4)
        return opts

    except Exception as e:
        print(f"Error generating profile: {e}")
        if proxy:
            update_proxy_stats(proxy, False)
        raise


def get_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Get or create a profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        Profile configuration dictionary
    """
    if config_exists(name=name):
        with open(opts_path(name)) as opts_file:
            return json.load(opts_file)
    return generate_profile(name, proxy)


class CreationPageStateFinder:
    """Class to detect the current state of PlayStation account creation pages."""

    def __init__(self, page) -> None:
        """Initialize the PageStateFinder.

        Args:
            page: The CamouFox page object
        """
        self._page = page
        self.page_state = CreationPageState.UNKNOWN

    def get_state(self) -> CreationPageState:
        """Detect the current page state.

        Returns:
            The detected page state
        """
        try:
            # Check for sign in button on home page
            if self._page.locator(CreationSelectors.SIGN_IN_BUTTON).first.is_visible():
                self.page_state = CreationPageState.CAN_CLICK_SIGN_IN
                return self.page_state
            
            # Check for create account link
            if self._page.locator(CreationSelectors.CREATE_ACCOUNT_LINK).first.is_visible():
                self.page_state = CreationPageState.CAN_CLICK_CREATE_ACCOUNT
                return self.page_state
            
            # Check for registration form
            if self._page.locator(CreationSelectors.EMAIL_INPUT).first.is_visible():
                self.page_state = CreationPageState.ON_REGISTRATION_FORM
                return self.page_state
            
            # Check for captcha
            if self._page.locator(CreationSelectors.CAPTCHA_IFRAME).first.is_visible():
                self.page_state = CreationPageState.CAPTCHA_REQUIRED
                return self.page_state
            
            # Check for email verification
            if self._page.locator(CreationSelectors.VERIFICATION_CODE_INPUT).first.is_visible():
                self.page_state = CreationPageState.EMAIL_VERIFICATION_REQUIRED
                return self.page_state
            
            # Check for error messages
            if self._page.locator(CreationSelectors.ERROR_MESSAGE).first.is_visible():
                self.page_state = CreationPageState.ERROR_PAGE
                return self.page_state

            self.page_state = CreationPageState.ON_HOME_PAGE
            return self.page_state

        except Exception as e:
            print(f"Error detecting page state: {e}")
            self.page_state = CreationPageState.UNKNOWN
            return self.page_state


class CreationController:
    """Controller class for automating PlayStation account creation."""

    def __init__(self, page, identity: Dict[str, str]) -> None:
        """Initialize the CreationController.

        Args:
            page: The CamouFox page object
            identity: Dictionary containing identity data (email, password, name, etc.)
        """
        self._page = page
        self._identity = identity

    def navigate_to_home(self) -> None:
        """Navigate to PlayStation home page."""
        print("Navigating to PlayStation home page")
        self._page.goto("https://playstation.com")
        time.sleep(3)

    def click_sign_in(self) -> None:
        """Click the sign in button to access login/registration options."""
        print("Clicking sign in button")
        button = self._page.locator(CreationSelectors.SIGN_IN_BUTTON).first
        button.click()
        time.sleep(2)

    def click_create_account(self) -> None:
        """Click the create account link."""
        print("Clicking create account link")
        link = self._page.locator(CreationSelectors.CREATE_ACCOUNT_LINK).first
        link.click()
        time.sleep(3)

    def fill_registration_form(self) -> None:
        """Fill out the PlayStation account registration form."""
        print("Filling registration form")

        # Fill email
        email_input = self._page.locator(CreationSelectors.EMAIL_INPUT).first
        email_input.click()
        email_input.type(self._identity['email'])
        time.sleep(0.5)

        # Fill password
        password_input = self._page.locator(CreationSelectors.PASSWORD_INPUT).first
        password_input.click()
        password_input.type(self._identity['password'])
        time.sleep(0.5)

        # Confirm password
        confirm_password_input = self._page.locator(CreationSelectors.CONFIRM_PASSWORD_INPUT).first
        confirm_password_input.click()
        confirm_password_input.type(self._identity['password'])
        time.sleep(0.5)

        # Fill first name
        first_name_input = self._page.locator(CreationSelectors.FIRST_NAME_INPUT).first
        first_name_input.click()
        first_name_input.type(self._identity['name']['first_name'])
        time.sleep(0.5)

        # Fill last name
        last_name_input = self._page.locator(CreationSelectors.LAST_NAME_INPUT).first
        last_name_input.click()
        last_name_input.type(self._identity['name']['last_name'])
        time.sleep(0.5)

    def fill_date_of_birth(self) -> None:
        """Fill date of birth fields."""
        print("Filling date of birth")
        dob = self._identity['dob']

        # Month
        month_select = self._page.locator(CreationSelectors.DOB_MONTH_SELECT).first
        month_select.select_option(str(dob.month))
        time.sleep(0.3)

        # Day
        day_select = self._page.locator(CreationSelectors.DOB_DAY_SELECT).first
        day_select.select_option(str(dob.day))
        time.sleep(0.3)

        # Year
        year_select = self._page.locator(CreationSelectors.DOB_YEAR_SELECT).first
        year_select.select_option(str(dob.year))
        time.sleep(0.3)

    def fill_location_info(self) -> None:
        """Fill location information."""
        print("Filling location information")

        # Country (Mexico)
        country_select = self._page.locator(CreationSelectors.COUNTRY_SELECT).first
        country_select.select_option("MX")
        time.sleep(0.5)

        # State/Province
        state_select = self._page.locator(CreationSelectors.STATE_SELECT).first
        state_select.select_option(self._identity['province'])
        time.sleep(0.5)

        # City
        city_input = self._page.locator(CreationSelectors.CITY_INPUT).first
        city_input.click()
        city_input.type(self._identity['city'])
        time.sleep(0.5)

        # Postal code
        postal_input = self._page.locator(CreationSelectors.POSTAL_CODE_INPUT).first
        postal_input.click()
        postal_input.type(self._identity['postcode'])
        time.sleep(0.5)

    def accept_terms(self) -> None:
        """Accept terms of service and privacy policy."""
        print("Accepting terms and conditions")

        # Terms of service
        terms_checkbox = self._page.locator(CreationSelectors.TERMS_CHECKBOX).first
        if not terms_checkbox.is_checked():
            terms_checkbox.click()
            time.sleep(0.3)

        # Privacy policy
        privacy_checkbox = self._page.locator(CreationSelectors.PRIVACY_CHECKBOX).first
        if not privacy_checkbox.is_checked():
            privacy_checkbox.click()
            time.sleep(0.3)

    def submit_registration(self) -> None:
        """Submit the registration form."""
        print("Submitting registration form")
        submit_button = self._page.locator(CreationSelectors.CREATE_ACCOUNT_BUTTON).first
        submit_button.click()
        time.sleep(3)

    def handle_captcha(self) -> None:
        """Handle captcha verification (manual intervention required)."""
        print("CAPTCHA detected! Please solve the captcha manually.")
        print("Press Enter after solving the captcha to continue...")
        input()

    def handle_email_verification(self) -> None:
        """Handle email verification process."""
        print("Email verification required")

        # Get verification link from email
        try:
            verification_link = get_email_verification_link(self._identity['email'])
            print(f"Got verification link: {verification_link}")

            # Navigate to verification link
            self._page.goto(verification_link)
            time.sleep(3)

        except Exception as e:
            print(f"Error getting verification link: {e}")
            print("Please check email manually and click verification link")
            input("Press Enter after clicking verification link...")

    def get_sso_code(self) -> str:
        """Extract SSO code after successful account creation."""
        print("Extracting SSO code")
        try:
            self._page.goto('https://ca.account.sony.com/api/v1/ssocookie')
            time.sleep(2)

            # Get the SSO content
            sso_content = self._page.locator("body > pre").text_content()
            sso_data = json.loads(sso_content)
            npsso = sso_data.get('npsso')

            if npsso:
                print(f"SSO code extracted: {npsso}")
                return npsso
            else:
                print("No SSO code found")
                return None

        except Exception as e:
            print(f"Error extracting SSO code: {e}")
            return None

    def wait_and_check_state(self, timeout: int = 30) -> None:
        """Wait and periodically check page state."""
        print(f"Waiting {timeout} seconds and checking page state")
        time.sleep(timeout)


def setup_profile(profile_name: str, proxy: Optional[Dict[str, str]] = None) -> tuple[Dict[str, Any], bool]:
    """Set up browser profile with proxy configuration.

    Args:
        profile_name: Name of the profile
        proxy: Optional proxy configuration

    Returns:
        Tuple of (profile_options, success_flag)
    """
    try:
        if proxy is None:
            proxy = get_proxy()
            print(f"Using proxy: {proxy['server']}")

        profile_opts = get_profile(profile_name, proxy)
        return profile_opts, True

    except Exception as e:
        print(f"Error setting up profile: {e}")
        return {}, False


def attempt_to_connect(page, proxy: Optional[Dict[str, str]] = None) -> tuple[bool, bool]:
    """Attempt to connect to PlayStation website.

    Args:
        page: CamouFox page object
        proxy: Proxy configuration

    Returns:
        Tuple of (connection_success, proxy_issue)
    """
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            print(f"Connection attempt {attempt + 1}/{max_attempts}")
            page.goto('https://playstation.com', timeout=30000)
            time.sleep(3)

            # Check if page loaded successfully
            if "PlayStation" in page.title():
                print("Successfully connected to PlayStation website")
                if proxy:
                    update_proxy_stats(proxy, True)
                return True, False
            else:
                print("Page loaded but title doesn't contain 'PlayStation'")

        except Exception as e:
            print(f"Connection attempt {attempt + 1} failed: {e}")
            if attempt == max_attempts - 1:
                if proxy:
                    update_proxy_stats(proxy, False)
                    print(f"Proxy failed to connect: {proxy['server']}")
                return False, True
            time.sleep(5)

    return False, True


def execute_creation_state_machine(page, identity: Dict[str, str]) -> bool:
    """Execute the state machine for account creation.

    Args:
        page: The CamouFox page object
        identity: Dictionary containing identity details

    Returns:
        bool: True if successful, False otherwise
    """
    # Initialize state finder and controller
    psf = CreationPageStateFinder(page)
    controller = CreationController(page, identity)

    # Map states to actions
    state_action_map = {
        CreationPageState.ON_HOME_PAGE: controller.navigate_to_home,
        CreationPageState.CAN_CLICK_SIGN_IN: controller.click_sign_in,
        CreationPageState.CAN_CLICK_CREATE_ACCOUNT: controller.click_create_account,
        CreationPageState.ON_REGISTRATION_FORM: lambda: (
            controller.fill_registration_form(),
            controller.fill_date_of_birth(),
            controller.fill_location_info(),
            controller.accept_terms(),
            controller.submit_registration()
        ),
        CreationPageState.CAPTCHA_REQUIRED: controller.handle_captcha,
        CreationPageState.EMAIL_VERIFICATION_REQUIRED: controller.handle_email_verification,
        CreationPageState.ERROR_PAGE: controller.wait_and_check_state,
    }

    # Define valid state transitions
    state_transition_map = {
        CreationPageState.ON_HOME_PAGE: [
            CreationPageState.CAN_CLICK_SIGN_IN,
            CreationPageState.ON_HOME_PAGE
        ],
        CreationPageState.CAN_CLICK_SIGN_IN: [
            CreationPageState.CAN_CLICK_CREATE_ACCOUNT,
            CreationPageState.ON_REGISTRATION_FORM
        ],
        CreationPageState.CAN_CLICK_CREATE_ACCOUNT: [
            CreationPageState.ON_REGISTRATION_FORM
        ],
        CreationPageState.ON_REGISTRATION_FORM: [
            CreationPageState.CAPTCHA_REQUIRED,
            CreationPageState.EMAIL_VERIFICATION_REQUIRED,
            CreationPageState.ACCOUNT_CREATED,
            CreationPageState.ERROR_PAGE
        ],
        CreationPageState.CAPTCHA_REQUIRED: [
            CreationPageState.ON_REGISTRATION_FORM,
            CreationPageState.EMAIL_VERIFICATION_REQUIRED,
            CreationPageState.ACCOUNT_CREATED
        ],
        CreationPageState.EMAIL_VERIFICATION_REQUIRED: [
            CreationPageState.ACCOUNT_CREATED,
            CreationPageState.ERROR_PAGE
        ],
        CreationPageState.ERROR_PAGE: [
            CreationPageState.ON_HOME_PAGE,
            CreationPageState.CAN_CLICK_SIGN_IN,
            CreationPageState.ON_REGISTRATION_FORM
        ],
        CreationPageState.UNKNOWN: [
            CreationPageState.ON_HOME_PAGE,
            CreationPageState.CAN_CLICK_SIGN_IN,
            CreationPageState.ON_REGISTRATION_FORM,
            CreationPageState.CAPTCHA_REQUIRED,
            CreationPageState.EMAIL_VERIFICATION_REQUIRED,
            CreationPageState.ACCOUNT_CREATED
        ],
    }

    # Initialize state machine
    success = False
    current_state = psf.get_state()
    print(f"\n=== INITIAL STATE: {current_state} ===")
    max_iterations = 20
    iteration = 0

    # Main state machine loop
    while (current_state != CreationPageState.ACCOUNT_CREATED and
           current_state != CreationPageState.UNKNOWN and
           not success and iteration < max_iterations):

        iteration += 1
        print(f"\n=== ITERATION {iteration}: {current_state} ===")

        # Execute action for current state
        if current_state in state_action_map:
            try:
                action = state_action_map[current_state]
                if callable(action):
                    action()
                else:
                    # Handle tuple of actions
                    for sub_action in action:
                        if callable(sub_action):
                            sub_action()
                time.sleep(2)
            except Exception as e:
                print(f"Error executing action for state {current_state}: {e}")
                return False

        # Find next state
        time.sleep(3)
        next_state = psf.get_state()
        print(f"Next state: {next_state}")

        # Check if we've successfully created the account
        if next_state == CreationPageState.ACCOUNT_CREATED:
            success = True
            break

        # Validate state transition
        if (current_state in state_transition_map and
            next_state not in state_transition_map[current_state] and
            next_state != CreationPageState.UNKNOWN):
            print(f"Invalid state transition from {current_state} to {next_state}")
            # Continue anyway, might be a temporary state

        current_state = next_state

        # Check if we're stuck
        if current_state == CreationPageState.UNKNOWN:
            print("Reached unknown state. Exiting.")
            break

    if iteration >= max_iterations:
        print("Maximum iterations reached. Exiting.")
        return False

    return success


def save_account_to_database(identity: Dict[str, str], sso_code: str) -> bool:
    """Save the created account to the database.

    Args:
        identity: Identity information
        sso_code: SSO code for the account

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Use async context for database operations
        async def save_async():
            postgres = PostgrestClient()
            response = await postgres.register_identity(identity)
            if sso_code:
                await postgres.register_identity_sso(response['id'], sso_code)
            return response

        # Run the async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(save_async())
        loop.close()

        print(f"Account saved to database with ID: {result['id']}")
        return True

    except Exception as e:
        print(f"Error saving account to database: {e}")
        return False


def create_playstation_account(proxy: Optional[Dict[str, str]] = None) -> bool:
    """Create a new PlayStation account with automated process.

    Args:
        proxy: Optional proxy configuration

    Returns:
        bool: True if successful, False otherwise
    """
    print("=== Starting PlayStation Account Creation ===")

    # Step 1: Generate identity
    print("\n1. Generating identity...")
    try:
        identity = IdentityGen()
        print(f"Generated identity for: {identity['email']}")
        print(f"Name: {identity['name']['first_name']} {identity['name']['last_name']}")
        print(f"DOB: {identity['dob']}")
        print(f"Location: {identity['city']}, {identity['province']}")
    except Exception as e:
        print(f"Error generating identity: {e}")
        return False

    # Step 2: Set up email
    print("\n2. Setting up email account...")
    try:
        add_user(identity['email'])
        print(f"Email account created: {identity['email']}")
    except Exception as e:
        print(f"Error setting up email: {e}")
        return False

    # Step 3: Set up browser profile
    print("\n3. Setting up browser profile...")
    profile_name = identity['email'].split('@')[0]
    profile_opts, setup_success = setup_profile(profile_name, proxy)
    if not setup_success:
        print("Failed to set up browser profile")
        return False

    # Step 4: Launch browser and create account
    print("\n4. Launching browser and creating account...")
    try:
        with Camoufox(from_options=profile_opts, persistent_context=True, headless=False) as browser:
            page = browser.new_page()

            # Connect to PlayStation website
            connection_success, proxy_issue = attempt_to_connect(page, proxy)
            if not connection_success:
                print("Failed to connect to PlayStation website")
                return False

            # Execute account creation process
            creation_success = execute_creation_state_machine(page, identity)
            if not creation_success:
                print("Failed to create account")
                return False

            print("\n5. Account creation completed successfully!")

            # Step 5: Extract SSO code
            print("\n6. Extracting SSO code...")
            controller = CreationController(page, identity)
            sso_code = controller.get_sso_code()

            # Step 6: Save to database
            print("\n7. Saving account to database...")
            if save_account_to_database(identity, sso_code):
                print("Account successfully saved to database")
            else:
                print("Warning: Account created but not saved to database")

            print(f"\n=== ACCOUNT CREATION COMPLETE ===")
            print(f"Email: {identity['email']}")
            print(f"Password: {identity['password']}")
            print(f"Username: {identity['username']}")
            if sso_code:
                print(f"SSO Code: {sso_code}")

            return True

    except Exception as e:
        print(f"Error during account creation: {e}")
        return False


def main():
    """Main function to run the account creation script."""
    print("PlayStation Account Creator")
    print("=" * 50)

    try:
        # Create account with automatic proxy selection
        success = create_playstation_account()

        if success:
            print("\n✅ Account creation completed successfully!")
        else:
            print("\n❌ Account creation failed!")

    except KeyboardInterrupt:
        print("\n\nScript interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")


if __name__ == "__main__":
    main()
