"""PlayStation account creation automation using CamouFox.

This module provides functionality to automate PlayStation account creation
using CamouFox with human-like interactions.
"""

import os
import json
import time
import re
from enum import Enum
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime

import pytz
from camoufox import Camoufox, launch_options
from auto.ps_helper.mail_util import add_user
from auto.ps_helper.create_identity import IdentityGen
from auto.ps_helper.get_email_hyperlink import get_link
from auto.camoufox.mail import get_email_verification_link
from auto.ps_helper.postgres import PostgrestClient
from auto.camoufox.proxy_manager import get_proxy
from auto.camoufox.account_query_manager import update_sso_code
import sys
import asyncio

if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

def save_account_to_database_sync(identity: Dict[str, str], sso_code: str) -> bool:
    """Save the created account to the database synchronously.

    Args:
        identity: Identity information
        sso_code: SSO code for the account

    Returns:
        bool: True if successful, False otherwise
    """
    async def save_async():
        try:
            postgres = PostgrestClient()
            response = await postgres.register_identity(identity)
            if sso_code:
                await postgres.register_identity_sso(response['id'], sso_code)
            return response
        except Exception as e:
            print(f"❌ Error saving account to database: {e}")
            return None

    try:
        # Run the async function in a new event loop
        if asyncio.get_event_loop().is_running():
            # If we're already in an event loop, create a new one in a thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, save_async())
                result = future.result()
        else:
            result = asyncio.run(save_async())
        
        if result:
            print(f"✅ Account saved to database with ID: {result['id']}")
            return True
        return False
    except Exception as e:
        print(f"❌ Error in database operation: {e}")
        return False

# Selectors for PlayStation account creation elements
class Selectors:
    # Home page sign in button
    SIGN_IN_BUTTON = "button[data-qa='web-toolbar#signin-button']"

    # Create account page
    CREATE_ACCOUNT_BUTTON = "button[data-qa='button-secondary']"

    # Create page
    CREATE_BUTTON = "button[data-dqa-button='primary']"

    # Country selection
    COUNTRY_SELECT = "select[data-dqa-pulldown='country']"
    NEXT_BUTTON = "button[data-dqa-button='next']"

    # Date of birth
    DAY_SELECT = "select[data-dqa-pulldown='bday-day']"
    MONTH_SELECT = "select[data-dqa-pulldown='bday-month']"
    YEAR_SELECT = "select[data-dqa-pulldown='bday-year']"

    # Email and password
    EMAIL_INPUT = "input[data-dqa-text='email']"
    PASSWORD_INPUT = "input[data-dqa-text='new-password']"
    PASSWORD_CONFIRM_INPUT = "input[data-dqa-text='new-password'][placeholder*='Vuelve a introducir']"

    # Captcha
    CAPTCHA_BUTTON = "button[data-theme='home.verifyButton']"
    CAPTCHA_BUTTON_ALT = "button[aria-label*='Desafío visual']"
    CAPTCHA_TEXT = "Iniciar rompecabezas"

    # Location
    CITY_INPUT = "input[data-dqa-text='address-level2']"
    PROVINCE_SELECT = "select[data-dqa-pulldown=''][name='address-level1']"
    POSTCODE_INPUT = "input[data-dqa-text='postal-code']"

    # ID and names
    ID_INPUT = "input[title='ID online']"
    FIRST_NAME_INPUT = "input[data-dqa-text='given-name']"
    LAST_NAME_INPUT = "input[data-dqa-text='family-name']"

    # Accept buttons
    ACCEPT_CREATE_BUTTON = "button[data-dqa-button='primary']"
    ACCEPT_BUTTON = "button[data-dqa-button='primary']"
    SIGUIENTE_BUTTON = "button[data-dqa-button='primary']"
    CONFIRM_BUTTON = "button[data-dqa-button='primary']"
    VERIFIED_BUTTON = "button[data-dqa-button='primary']"

    # Error messages
    ERROR_TEXTS = [
        "Algo ha salido mal",
        "Something has gone wrong",
        "Este dispositivo ha enviado demasiadas solicitudes al servidor. Inténtalo de nuevo más tarde"
    ]

    # Bot detection messages
    BOT_DETECTION_TEXTS = [
        "No se ha podido conectar con el servidor",
        "Can't connect to the server"
    ]

    # Error symbol that indicates bot detection
    ERROR_SYMBOL = "div.psw-icon-size-4.icon--HzoyX"

    # SSO cookie page
    SSO_CONTENT = "body > pre"


class PageState(str, Enum):
    UNKNOWN = "unknown"
    ON_HOME_PAGE = "on_home_page"
    CAN_CLICK_SIGN_IN = "can_click_sign_in"
    ON_CREATE_ACCOUNT_PAGE = "on_create_account_page"
    ON_COUNTRY_PAGE = "on_country_page"
    ON_DOB_PAGE = "on_dob_page"
    ON_EMAIL_PASSWORD_PAGE = "on_email_password_page"
    ON_CAPTCHA_PAGE = "on_captcha_page"
    ON_LOCATION_PAGE = "on_location_page"
    ON_ID_PAGE = "on_id_page"
    ON_BUTTON_CLICK_PAGE = "on_button_click_page"  # Generic page that just needs a button click
    ON_VERIFY_PAGE = "on_verify_page"  # Page that needs email verification
    IS_SIGNED_IN = "is_signed_in"  # Ready for SSO extraction
    ACCOUNT_CREATED = "account_created"
    SOMETHING_WENT_WRONG_PAGE = "something went wrong"
    BOT_DETECTED = "bot detected"


def opts_path(name: str) -> str:
    return f"auto/camoufox/profiles/{name}/opts.json"


def config_exists(name: str) -> bool:
    return Path(opts_path(name)).exists()

def generate_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Generate a new profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        New profile configuration dictionary
    """
    profile_dir = os.path.dirname(opts_path(name))

    try:
        # Determine the correct OS based on the current platform
        browser_os = ['windows'] if sys.platform == 'win32' else ['linux']

        opts = launch_options(
            user_data_dir=profile_dir,
            os=browser_os,
            proxy=proxy,
            geoip=True,
            window=(1080, 720),
            humanize=True,
            headless=False
        )

        os.makedirs(profile_dir, exist_ok=True)
        with open(opts_path(name), "w") as opts_file:
            json.dump(opts, opts_file, indent=4)
        return opts

    except Exception as e:
        print(f"Error generating profile: {e}")
        raise


class PageStateFinder:
    """Class to determine the current state of the PlayStation account creation flow."""

    def __init__(self, page) -> None:
        """Initialize the PageStateFinder.

        Args:
            page: The CamouFox page object
        """
        self._page = page
        self.page_state = PageState.UNKNOWN

    def get_state(self) -> PageState:
        """Determine the current state of the page.

        Returns:
            The detected page state
        """
        # Check for error pages
        for error_text in Selectors.ERROR_TEXTS:
            if self._page.get_by_text(error_text).is_visible():
                print("Something went wrong. Refreshing page")
                self.page_state = PageState.SOMETHING_WENT_WRONG_PAGE
                return self.page_state

        # Check for bot detection
        for bot_text in Selectors.BOT_DETECTION_TEXTS:
            if self._page.get_by_text(bot_text).is_visible():
                print("Detected as bot, abort program")
                self.page_state = PageState.BOT_DETECTED
                return self.page_state

        # Check for error symbol that indicates bot detection
        if self._page.locator(Selectors.ERROR_SYMBOL).is_visible():
            error_element = self._page.locator(Selectors.ERROR_SYMBOL).first
            aria_label = "unknown"
            try:
                aria_label = error_element.get_attribute("aria-label") or "unknown"
            except:
                pass
            print(f"Error symbol detected (aria-label: {aria_label}) - treating as bot detection")
            self.page_state = PageState.BOT_DETECTED
            return self.page_state

        # Check for PlayStation homepage
        if self._page.url.startswith('https://www.playstation.com') or self._page.url.startswith('https://playstation.com'):
            if self._page.locator(Selectors.SIGN_IN_BUTTON).first.is_visible():
                self.page_state = PageState.CAN_CLICK_SIGN_IN
                return self.page_state
            else:
                # If we're on PlayStation homepage but no sign-in button, user is likely signed in
                print("DEBUG: On PlayStation homepage without sign-in button - IS_SIGNED_IN")
                self.page_state = PageState.IS_SIGNED_IN
                return self.page_state

        # Check for account creation flow pages
        if self._page.locator(Selectors.CREATE_ACCOUNT_BUTTON).first.is_visible():
            self.page_state = PageState.ON_CREATE_ACCOUNT_PAGE
            return self.page_state



        if self._page.locator(Selectors.COUNTRY_SELECT).first.is_visible():
            self.page_state = PageState.ON_COUNTRY_PAGE
            return self.page_state

        if self._page.locator(Selectors.DAY_SELECT).first.is_visible():
            self.page_state = PageState.ON_DOB_PAGE
            return self.page_state

        if self._page.locator(Selectors.EMAIL_INPUT).first.is_visible():
            self.page_state = PageState.ON_EMAIL_PASSWORD_PAGE
            return self.page_state

        # Check for CAPTCHA page with multiple selectors
        if (self._page.locator(Selectors.CAPTCHA_BUTTON).first.is_visible() or
            self._page.locator(Selectors.CAPTCHA_BUTTON_ALT).first.is_visible() or
            self._page.get_by_text(Selectors.CAPTCHA_TEXT).first.is_visible()):
            self.page_state = PageState.ON_CAPTCHA_PAGE
            return self.page_state

        if self._page.locator(Selectors.CITY_INPUT).first.is_visible():
            self.page_state = PageState.ON_LOCATION_PAGE
            return self.page_state

        if (self._page.locator(Selectors.ID_INPUT).first.is_visible() or
            self._page.locator(Selectors.FIRST_NAME_INPUT).first.is_visible()):
            self.page_state = PageState.ON_ID_PAGE
            return self.page_state

        # Special check for verification page first (needs email handling)
        # Try multiple ways to detect the verification page
        verification_detected = False

        
        # Method 1: Check for verification page heading text
        try:
            caption_in_button = "Ya se verificó"
            if self._page.get_by_text("Verificar tu dirección de correo electrónico").first.is_visible():
                print("DEBUG: Found 'Verificar tu dirección de correo electrónico' text - ON_VERIFY_PAGE")
                verification_detected = True
        except Exception as e:
            print()
            print("-------------")
            print(e)
            print("-------------")
            print()
            pass

        # Method 2: Check for "Ya se verificó" button text
        try:
            if self._page.get_by_text("Ya se verificó").first.is_visible():
                print("DEBUG: Found 'Ya se verificó' button text - ON_VERIFY_PAGE")
                verification_detected = True
        except Exception as e:
            print()
            print("-------------")
            print(e)
            print("-------------")
            print()
            pass


        if verification_detected:
            self.page_state = PageState.ON_VERIFY_PAGE
            return self.page_state

        # Check for any button that just needs clicking (generic button click pages)
        # These include: Create, Aceptar y crear cuenta, Aceptar, Siguiente, Confirmar
        button_texts_to_check = [
            "Crear",
            "Create",
            "Aceptar y crear cuenta",
            "Aceptar",
            "Siguiente",
            "Confirmar"
        ]

        for button_text in button_texts_to_check:
            try:
                if self._page.get_by_text(button_text).is_visible():
                    print(f"DEBUG: Found '{button_text}' button - ON_BUTTON_CLICK_PAGE")
                    self.page_state = PageState.ON_BUTTON_CLICK_PAGE
                    return self.page_state
                aria_lable = "Create an Account"
                if self._page.get_by_role("button", name=aria_lable).first.is_visible():
                    print(f"DEBUG: Found '{aria_lable}' button - ON_BUTTON_CLICK_PAGE")
                    self.page_state = PageState.ON_BUTTON_CLICK_PAGE
                    return self.page_state
            except:
                continue

        self.page_state = PageState.UNKNOWN
        return self.page_state


class Controller:
    """Controller class for interacting with the PlayStation account creation flow."""

    def __init__(self, page, identity: Dict[str, str]) -> None:
        """Initialize the Controller.

        Args:
            page: The CamouFox page object
            identity: Dictionary containing identity data
        """
        self._page = page
        self._identity = identity

    def click_sign_in(self) -> None:
        """Click the sign in button on the homepage."""
        print("Clicking sign in")
        button = self._page.locator(Selectors.SIGN_IN_BUTTON).first
        button.click()
        time.sleep(2)

    def click_create_account(self) -> None:
        """Click the create account button."""
        print("Clicking create account")
        button = self._page.locator(Selectors.CREATE_ACCOUNT_BUTTON).first
        button.click()
        time.sleep(2)

    def click_create(self) -> None:
        """Click the create button."""
        print("Clicking create")
        button = self._page.locator(Selectors.CREATE_BUTTON).first
        button.click()
        time.sleep(2)

    def select_country_and_next(self) -> None:
        """Select Mexico as the country and click next."""
        print("Selecting country: Mexico")
        select = self._page.locator(Selectors.COUNTRY_SELECT).first
        select.select_option("MX")
        time.sleep(1)
        self.click_next()

    def click_next(self) -> None:
        """Click the next button."""
        print("Clicking next")
        button = self._page.locator(Selectors.NEXT_BUTTON).first
        button.click()
        time.sleep(2)

    def fill_dob_and_next(self) -> None:
        """Fill in the date of birth fields and click next."""
        print("Filling date of birth")
        dob = self._identity['dob']  # Format: YYYY-MM-DD
        year, month, day = dob.split('-')

        # Select day
        day_select = self._page.locator(Selectors.DAY_SELECT).first
        day_select.select_option(str(int(day)))  # Remove leading zero
        time.sleep(0.5)

        # Select month
        month_select = self._page.locator(Selectors.MONTH_SELECT).first
        month_select.select_option(str(int(month)))  # Remove leading zero
        time.sleep(0.5)

        # Select year
        year_select = self._page.locator(Selectors.YEAR_SELECT).first
        year_select.select_option(year)
        time.sleep(0.5)

        self.click_next()

    def fill_email_password_and_next(self) -> None:
        """Fill in email and password fields and click next."""
        print("Filling email and password")

        # Fill email
        email_input = self._page.locator(Selectors.EMAIL_INPUT).first
        email_input.click()
        email_input.type(self._identity['email'])
        # Click outside to blur the field
        self._page.click('body')
        time.sleep(1)

        # Fill password
        password_input = self._page.locator(Selectors.PASSWORD_INPUT).first
        password_input.click()
        password_input.type(self._identity['password'])
        # Click outside to blur the field
        self._page.click('body')
        time.sleep(1)

        # Confirm password
        password_confirm_input = self._page.locator(Selectors.PASSWORD_CONFIRM_INPUT).first
        password_confirm_input.click()
        password_confirm_input.type(self._identity['password'])
        # Click outside to blur the field
        self._page.click('body')
        time.sleep(1)

        self.click_next()

    def handle_captcha(self) -> None:
        """Handle captcha - this requires human interaction."""
        print("CAPTCHA detected - Human interaction required!")
        print("Please solve the captcha manually and then press Enter to continue...")
        input("Press Enter after solving the captcha...")

    def fill_location_and_next(self) -> None:
        """Fill in location information and click next."""
        print("Filling location information")

        # Fill city
        city_input = self._page.locator(Selectors.CITY_INPUT).first
        city_input.click()
        city_input.type(self._identity['city'])
        # Click outside to blur the field
        self._page.click('body')
        time.sleep(1)

        # Select province
        province_select = self._page.locator(Selectors.PROVINCE_SELECT).first
        province_select.select_option("DIF")  # Distrito Federal
        time.sleep(1)

        # Fill postcode
        postcode_input = self._page.locator(Selectors.POSTCODE_INPUT).first
        postcode_input.click()
        postcode_input.type(self._identity['postcode'])
        # Click outside to blur the field
        self._page.click('body')
        time.sleep(1)

        self.click_next()

    def fill_id_names_and_next(self) -> None:
        """Fill in ID and name information and click next."""
        print("Filling ID and names")

        # Fill ID (username)
        id_input = self._page.locator(Selectors.ID_INPUT).first
        id_input.click()
        id_input.type(self._identity['username'])
        # Click outside to blur the field
        self._page.click('body')
        time.sleep(1)

        # Fill first name
        first_name_input = self._page.locator(Selectors.FIRST_NAME_INPUT).first
        first_name_input.click()
        first_name_input.type(self._identity['firstname'])
        # Click outside to blur the field
        self._page.click('body')
        time.sleep(1)

        # Fill last name
        last_name_input = self._page.locator(Selectors.LAST_NAME_INPUT).first
        last_name_input.click()
        last_name_input.type(self._identity['lastname'])
        # Click outside to blur the field
        self._page.click('body')
        time.sleep(1)

        self.click_next()

    def click_accept_create(self) -> None:
        """Click accept and create account button."""
        print("Clicking accept and create account")
        button = self._page.get_by_text("Aceptar y crear cuenta").first
        button.click()
        time.sleep(3)

    def click_accept(self) -> None:
        """Click accept button."""
        print("Clicking accept")
        button = self._page.get_by_text("Aceptar").first
        button.click()
        time.sleep(2)

    def click_siguiente(self) -> None:
        """Click siguiente (next) button."""
        print("Clicking siguiente")
        button = self._page.get_by_text("Siguiente").first
        button.click()
        time.sleep(2)

    def click_confirm(self) -> None:
        """Click confirm button."""
        print("Clicking confirm")
        button = self._page.get_by_text("Confirmar").first
        button.click()
        time.sleep(2)

    def handle_verification_and_verified(self) -> None:
        """Handle email verification and click verified."""
        print("🔗 Getting verification link...")
        try:
            time.sleep(10)
            link = get_email_verification_link(self._identity['email'])
            print(f"Got verification link: {link}")
            self._page.goto(link)
            time.sleep(5)

            print("🔘 Clicking verified button")
            clicked = False

            if not clicked:
                try:
                    button = self._page.locator("button[data-dqa-button='primary']").first
                    if button.is_visible():
                        button.click()
                        clicked = True
                        print("✅ Clicked primary button")
                except Exception as e:
                    print(f"Method 3 failed: {e}")

            if not clicked:
                print("❌ Could not find verification button")

            time.sleep(2)

        except Exception as e:
            print(f"Error getting verification link: {e}")
            print("Please manually verify the email and continue...")

    def click_generic_button(self) -> None:
        """Click any generic primary button on button click pages."""
        print("🔘 Clicking generic primary button")

        # Try to find and click any primary button
        clicked = False

        try:
            # Method 1: Look for primary button with data attribute
            button = self._page.locator("button[data-dqa-button='primary']").first
            if button.is_visible():
                button.click()
                clicked = True
                print("✅ Clicked primary button via data attribute")
        except Exception as e:
            print(f"Method 1 failed: {e}")

        if not clicked:
            try:
                # Method 2: Look for any visible button
                buttons = self._page.locator("button")
                for i in range(min(buttons.count(), 5)):  # Check first 5 buttons
                    button = buttons.nth(i)
                    if button.is_visible():
                        button.click()
                        clicked = True
                        print(f"✅ Clicked button {i+1}")
                        break
            except Exception as e:
                print(f"Method 2 failed: {e}")

        if not clicked:
            print("❌ Could not find any clickable button")

        time.sleep(3)

    def get_sso(self) -> str:
        """Get and save the SSO code."""
        print("🔑 Navigating to SSO cookie page")
        self._page.goto('https://ca.account.sony.com/api/v1/ssocookie')
        time.sleep(2)
        text = self._page.locator(Selectors.SSO_CONTENT).text_content()
        npsso = json.loads(text)['npsso']
        print(f"✅ SSO code extracted: {npsso}")

        # Update the SSO code in the database
        if npsso:
            try:
                save_account_to_database_sync(self._identity, npsso)
            except Exception as e:
                print(f"❌ Error updating SSO code in database: {e}")

        return npsso

    def refresh_page(self) -> None:
        """Reload the current page."""
        print("Reloading page")
        self._page.reload()
        time.sleep(3)

    def wait_on_page(self) -> None:
        """Wait on the current page for a short time."""
        print("Waiting on page")
        time.sleep(3)


def get_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Get or create a profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        Profile configuration dictionary
    """
    if config_exists(name=name):
        with open(opts_path(name)) as opts_file:
            return json.load(opts_file)
    return generate_profile(name, proxy)


def execute_state_machine(page, identity: Dict[str, str]) -> bool:
    """
    Execute the state machine for account creation.

    Args:
        page: The CamouFox page object
        identity: Dictionary containing identity details

    Returns:
        bool: True if successful, False otherwise
    """
    # Initialize state finder and controller
    psf = PageStateFinder(page)
    controller = Controller(page, identity)

    # Map states to actions
    state_action_map = {
        PageState.ON_HOME_PAGE: controller.wait_on_page,
        PageState.CAN_CLICK_SIGN_IN: controller.click_sign_in,
        PageState.ON_CREATE_ACCOUNT_PAGE: controller.click_create_account,
        PageState.ON_COUNTRY_PAGE: controller.select_country_and_next,
        PageState.ON_DOB_PAGE: controller.fill_dob_and_next,
        PageState.ON_EMAIL_PASSWORD_PAGE: controller.fill_email_password_and_next,
        PageState.ON_CAPTCHA_PAGE: controller.handle_captcha,
        PageState.ON_LOCATION_PAGE: controller.fill_location_and_next,
        PageState.ON_ID_PAGE: controller.fill_id_names_and_next,
        PageState.ON_BUTTON_CLICK_PAGE: controller.click_generic_button,
        PageState.ON_VERIFY_PAGE: controller.handle_verification_and_verified,
        PageState.IS_SIGNED_IN: controller.get_sso,
        PageState.SOMETHING_WENT_WRONG_PAGE: controller.refresh_page,
    }

    # Define valid state transitions
    state_transition_map = {
        PageState.ON_HOME_PAGE: [PageState.CAN_CLICK_SIGN_IN, PageState.ON_HOME_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.ON_BUTTON_CLICK_PAGE],
        PageState.CAN_CLICK_SIGN_IN: [PageState.ON_CREATE_ACCOUNT_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.ON_BUTTON_CLICK_PAGE],
        PageState.ON_CREATE_ACCOUNT_PAGE: [PageState.ON_BUTTON_CLICK_PAGE, PageState.ON_COUNTRY_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE],
        PageState.ON_COUNTRY_PAGE: [PageState.ON_DOB_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.ON_BUTTON_CLICK_PAGE],
        PageState.ON_DOB_PAGE: [PageState.ON_EMAIL_PASSWORD_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.ON_BUTTON_CLICK_PAGE],
        PageState.ON_EMAIL_PASSWORD_PAGE: [PageState.ON_CAPTCHA_PAGE, PageState.ON_LOCATION_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.ON_BUTTON_CLICK_PAGE],
        PageState.ON_CAPTCHA_PAGE: [PageState.ON_LOCATION_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.ON_BUTTON_CLICK_PAGE],
        PageState.ON_LOCATION_PAGE: [PageState.ON_ID_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.ON_BUTTON_CLICK_PAGE],
        PageState.ON_ID_PAGE: [PageState.ON_BUTTON_CLICK_PAGE, PageState.SOMETHING_WENT_WRONG_PAGE],
        PageState.ON_BUTTON_CLICK_PAGE: [PageState.ON_COUNTRY_PAGE, PageState.ON_BUTTON_CLICK_PAGE, PageState.ON_VERIFY_PAGE, PageState.IS_SIGNED_IN, PageState.ACCOUNT_CREATED, PageState.SOMETHING_WENT_WRONG_PAGE],
        PageState.ON_VERIFY_PAGE: [PageState.ON_BUTTON_CLICK_PAGE, PageState.IS_SIGNED_IN, PageState.ACCOUNT_CREATED, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.CAN_CLICK_SIGN_IN],
        PageState.IS_SIGNED_IN: [PageState.ACCOUNT_CREATED, PageState.SOMETHING_WENT_WRONG_PAGE],
        PageState.SOMETHING_WENT_WRONG_PAGE: [PageState.ON_HOME_PAGE, PageState.CAN_CLICK_SIGN_IN, PageState.SOMETHING_WENT_WRONG_PAGE, PageState.ON_BUTTON_CLICK_PAGE],
        PageState.UNKNOWN: [PageState.ON_HOME_PAGE, PageState.CAN_CLICK_SIGN_IN, PageState.ON_CREATE_ACCOUNT_PAGE,
                           PageState.ON_COUNTRY_PAGE, PageState.ON_DOB_PAGE,
                           PageState.ON_EMAIL_PASSWORD_PAGE, PageState.ON_CAPTCHA_PAGE, PageState.ON_LOCATION_PAGE,
                           PageState.ON_ID_PAGE, PageState.ON_BUTTON_CLICK_PAGE, PageState.ON_VERIFY_PAGE,
                           PageState.IS_SIGNED_IN, PageState.SOMETHING_WENT_WRONG_PAGE],
    }

    # Initialize state machine
    bot_detected = False
    success = False
    current_state = psf.get_state()
    print(f"\n=== INITIAL STATE: {current_state} ===")

    # Main state machine loop
    max_iterations = 50  # Prevent infinite loops
    iteration = 0
    sso_code = None

    while (current_state != PageState.UNKNOWN and not success and not bot_detected and
           current_state != PageState.ACCOUNT_CREATED and iteration < max_iterations):

        iteration += 1
        print(f"\n=== ITERATION {iteration}: CURRENT STATE: {current_state} ===")

        # Execute action for current state
        action = state_action_map.get(current_state)
        if action:
            try:
                print(f"Executing action for state: {current_state}")
                action()
                time.sleep(2)  # Wait between actions
            except Exception as e:
                print(f"Error executing action for state {current_state}: {e}")
                if "Timeout" in str(e):
                    # Try to recover by refreshing
                    controller.refresh_page()
                else:
                    return False

        # Find next state
        possible_next_states = state_transition_map.get(current_state, [])
        found_next_state = False
        attempts = 0
        max_attempts = 100

        # Try to find a valid next state
        while attempts < max_attempts and not found_next_state:
            next_state = psf.get_state()
            print(f"=== DETECTED STATE: {next_state} ===")

            # Check for special states
            if next_state == PageState.BOT_DETECTED:
                print("Bot detected - aborting")
                bot_detected = True
                break

            if next_state == PageState.ACCOUNT_CREATED:
                print("Account creation completed!")
                success = True
                break

            # Handle SSO extraction when signed in
            if next_state == PageState.IS_SIGNED_IN:
                print("User is signed in, extracting SSO code...")
                try:
                    sso_code = controller.get_sso()
                    success = True
                    break
                except Exception as e:
                    print(f"Error extracting SSO code: {e}")
                    # Continue the loop to try again or handle error

            # Check if the next state is valid
            if next_state != PageState.UNKNOWN and next_state in possible_next_states:
                current_state = next_state
                found_next_state = True
            else:
                attempts += 1
                if attempts == max_attempts:
                    print("Failed to find valid next state, trying to recover...")
                    # page.goto('https://playstation.com/es-mx/')
                print(f"Attempt {attempts}/{max_attempts}: Waiting for state change...")
                time.sleep(3)

        # Check if we're stuck
        if not found_next_state and not success and not bot_detected:
            print("No valid transitions found. Exiting.")
            break

    # Return results
    if success:
        if sso_code:
            print(f"✅ Account creation completed with SSO code: {sso_code}")
            return sso_code
        else:
            print("✅ Account creation completed successfully")
            return True

    return success


def create_account_automated(identity: Optional[Dict[str, str]] = None, proxy: Optional[Dict[str, str]] = None) -> bool:
    """
    Automated PlayStation account creation.

    Args:
        identity: Optional identity data. If None, will generate new identity.
        proxy: Optional proxy configuration

    Returns:
        bool: True if successful, False otherwise
    """
    # Generate identity if not provided
    if identity is None:
        identity = IdentityGen()
        add_user(identity['email'])
        print("Generated new identity:")
        print(f"Email: {identity['email']}")
        print(f"Username: {identity['username']}")
        print(f"Password: {identity['password']}")

    # Get proxy if not provided
    if proxy is None:
        proxy = get_proxy()

    # Generate a unique profile name
    profile_name = f"ps_account_{int(time.time())}"

    # Get profile options with proxy
    try:
        profile_opts = get_profile(profile_name, proxy)
    except Exception as e:
        print(f"Error setting up profile: {e}")
        return False

    # Launch browser with the profile
    try:
        with Camoufox(from_options=profile_opts, persistent_context=True, headless=False, addons=["auto/camoufox/anticaptcha-plugin"]) as browser:
            page = browser.new_page()

            # Navigate to PlayStation Mexico site
            page.goto("https://playstation.com/es-mx/")
            time.sleep(3)

            # Execute the state machine
            result = execute_state_machine(page, identity)

            if result:
                print("Account creation completed successfully!")
                return True
            else:
                print("Account creation failed")
                return False

    except Exception as e:
        print(f"Error during browser session: {e}")
        return False


# Legacy async functions for manual compatibility
async def generate_identity():
    identity = IdentityGen()
    add_user(identity['email'])
    return identity

async def get_verification_email(email):
    link = await get_link(email)
    return link

async def start_browser():
    proxy = get_proxy()

    # Generate a unique profile name
    profile_name = f"ps_account_{int(time.time())}"

    # Get profile options with proxy
    profile_opts = get_profile(profile_name, proxy)

    # Launch CamouFox browser (using sync version now)
    try:
        with Camoufox(from_options=profile_opts, persistent_context=True, headless=False) as browser:
            page = browser.new_page()
            page.goto("https://playstation.com/es-mx/")
            return browser, page
        
    except Exception as e:
        print(f"Error launching browser: {e}")
        raise


async def close_browser(browser_data):
    browser, _ = browser_data
    browser.close()

async def submit_sso(identity:dict):
    sso = await async_input("\nEnter sso: ")
    if sso:
        match = re.search(r'"npsso":"([^"]+)"', sso)
        if match:
            npsso_value = match.group(1)
            postgres = PostgrestClient()
            response = await postgres.register_identity(identity)
            await postgres.register_identity_sso(response['id'], npsso_value)


async def copy_handler(info_required, identity):
    print(identity[info_required])
    # Copy to clipboard functionality removed to avoid dependency

async def async_input(prompt):
    return await asyncio.get_event_loop().run_in_executor(None, input, prompt)

async def show_menu(options, title=None):
    print()
    if title:
        print(title)
    for i, option in enumerate(options, 1):
        print(f"{i}: {option}")
    
    while True:
        try:
            choice = int(await async_input("\nEnter choice: "))
            if 1 <= choice <= len(options):
                return options[choice-1]
            print(f"Please enter 1-{len(options)}")
        except ValueError:
            print("Please enter a number")

async def run():
    browser_data = None
    identity = None
    finished = False
    while not finished:
        action = await show_menu([
            "Create Account (Automated)", "Start Browser", "Close Browser", "Generate Identity",
            "Get Verification Link", "Get SSO Code", "Submit SSO Code", "Copy DoB", "Copy City",
            "Copy Postcode", "Copy Email", "Copy Username", "Copy Password", "Copy Firstname",
            "Copy Lastname", "End Program"
        ])

        if action == "Create Account (Automated)":
            print("Starting automated account creation...")
            success = create_account_automated()
            if success:
                print("✅ Account created successfully!")
            else:
                print("❌ Account creation failed.")

        elif action == "Start Browser":
            if browser_data is None:
                browser_data = await start_browser()
                print("CamouFox browser started.")
            else:
                print("Browser is already running.")

        elif action == "Close Browser":
            if browser_data:
                await close_browser(browser_data)
                browser_data = None
                print("Browser closed.")
            else:
                print("No Browser to close.")

        elif action == "Generate Identity":
            identity = await generate_identity()
            print(identity)

        elif action == "Get Verification Link":
            if identity != None:
                link = await get_verification_email(identity['email'])
                print(f"Verification link: {link}")
            else:
                print("Haven't Created An Identity Yet")

        elif action == "Get SSO Code":
            print("https://ca.account.sony.com/api/v1/ssocookie")

        elif action == "Submit SSO Code":
            await submit_sso(identity)

        elif re.match(r'Copy', action):
            if identity != None:
                await copy_handler(" ".join(action.split(" ")[1:]).lower(), identity)

        elif action == "End Program":
            finished = True
            if browser_data != None:
                await close_browser(browser_data)

def main():
    """Main function for direct automated account creation."""
    print("PlayStation Account Creation Automation")
    print("=" * 40)

    try:
        success = create_account_automated()
        if success:
            print("\n✅ Account creation completed successfully!")
            return True
        else:
            print("\n❌ Account creation failed.")
            return False
    except KeyboardInterrupt:
        print("\n⚠️ Account creation interrupted by user.")
        return False
    except Exception as e:
        print(f"\n❌ Error during account creation: {e}")
        return False


if __name__ == "__main__":
    import sys
    NUMBER_OF_ACCOUNTS = 5

    # Check if user wants automated creation or interactive menu
    # if len(sys.argv) > 1 and sys.argv[1] == "--auto":
    if True:
        for i in range(NUMBER_OF_ACCOUNTS):
            print(f"Starting automated account creation {i+1}/{NUMBER_OF_ACCOUNTS}")
            success = main()
            if not success:
                print("Account creation failed. Retrying...")
            else:
                print("Account created successfully!")
    else:
        # Run the interactive menu
        import asyncio
        asyncio.run(run())




