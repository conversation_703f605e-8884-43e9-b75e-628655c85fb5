from mail_util import add_user
from create_identity import IdentityGen
from get_email_hyperlink import get_link
from pprint import pprint
import random
from antidetect.antidetect_browser import Antidetect
import threading
import re
import pyperclip
import csv

proxy_list = [
    "*************",
    "**************",
    "*************",
    "*************",
    "***********",
    "**************",
    "*************",
    "*************",
    "***********",
    "*************",
    "*************",
    "***************",
    "*************",
    "*************",
    "**************",
    "*************",
    "*************",
    "************",
    "**************",
    "*************",
    "************",
    "**************",
    "**************",
    "*************",
    "*************",
    "*************",
    "*************",
    "************",
    "*************",
    "**************",
    "************",
    "*************",
    "************",
    "*************",
    "************",
    "*************",
    "*************",
    "*************",
    "***********",
    "**************",
    "**************",
    "*************",
    "*************",
    "*************",
    "*************",
    "*************",
    "************",
    "**************",
    "*************",
    "************",
]

async def generate_identity():
    identity = IdentityGen()
    add_user(identity['email'])
    return identity

async def get_verification_email(email):
    link = await get_link(email)
    pyperclip.copy(link)
    return link

async def start_browser():
    host = random.choice(proxy_list)
    proxy = {
        'server': f'{host}:4444',
        'username': "b03f165be1",
        'password': "w1q4cGzT"
    }
    antidetect = await Antidetect().start()
    browser = await antidetect.new_browser(proxy_string=f"http://{proxy["username"]}:{proxy["password"]}@{proxy['server']}", headless=False)
    page = await browser.new_page()
    await page.goto("https://playstation.com/es-mx/")
    return browser

async def close_browser(browser):
    await browser.close()

async def submit_sso(identity:dict):
    sso = await async_input("\nEnter sso: ")
    identity['sso'] = sso
    fieldnames = ['dob', 'postcode', 'email', 'username', 'password', 'firstname', 'lastname', 'sso']
    identity = {key: value for key, value in identity.items() if key in fieldnames}
    with open("accounts.csv", mode='+a', newline="") as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writerows([identity])

async def copy_handler(info_required, identity):
    print(identity[info_required])
    pyperclip.copy(identity[info_required])

async def async_input(prompt):
    return await asyncio.get_event_loop().run_in_executor(None, input, prompt)

async def show_menu(options, title=None):
    print()
    if title:
        print(title)
    for i, option in enumerate(options, 1):
        print(f"{i}: {option}")
    
    while True:
        try:
            choice = int(await async_input("\nEnter choice: "))
            if 1 <= choice <= len(options):
                return options[choice-1]
            print(f"Please enter 1-{len(options)}")
        except ValueError:
            print("Please enter a number")

async def run():
    browser = None
    identity = None
    finished = False
    while not finished:
        action = await show_menu(["Start Browser", "Close Browser", "Generate Identity", "Get Verification Link", "Get SSO Code",
                                   "Submit SSO Code", "Copy DoB", "Copy City", "Copy Postcode", "Copy Email", "Copy Username",
                                    "Copy Password", "Copy Firstname", "Copy Lastname", "End Program"])
        if action == "Start Browser":
            if browser is None or browser.done():
                browser = await start_browser()
                print("Browser started in the background.")
            else:
                print("Browser is already running.")
        if action == "Close Browser":
            if browser:
                await close_browser(await browser)
                browser = None
                print("Browser closed.")
            else:
                print("No Browser to close.")
        if action == "Generate Identity":
            identity = await generate_identity()
            print(identity)
        if action == "Get Verification Link":
            if identity != None:
                await get_verification_email(identity['email'])
            else:
                print("Haven't Created An Identity Yet")
        if action == "Get SSO Code":
            print("https://ca.account.sony.com/api/v1/ssocookie")
            pyperclip.copy("https://ca.account.sony.com/api/v1/ssocookie")
        if action == "Submit SSO Code":
            await submit_sso(identity)
        if re.match(r'Copy', action):
            if identity != None:
                await copy_handler(" ".join(action.split(" ")[1:]).lower(), identity)
        if action == "End Program":
            finished = True
            if browser != None:
                await close_browser(browser)

if __name__ == "__main__":
    import asyncio
    asyncio.run(run())
