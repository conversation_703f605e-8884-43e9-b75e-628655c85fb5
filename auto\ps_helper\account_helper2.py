import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import asyncio
import threading
import pyperclip
import random
import re
import csv
from mail_util import add_user
from create_identity import IdentityGen
from get_email_hyperlink import get_link
from antidetect.antidetect_browser import Antidetect
from postgres import PostgrestClient
from create_new_gologin_profile import generate_new_profile
from go_login_profiles import get_one_by_email
from gologin import GoLogin
import os

proxy_hosts = {
    'rb-us-1': '*************',
    'rb-us-2': '**************',
    'rb-us-3': '*************',
    'rb-us-4': '*************',
    'rb-us-5': '***********', 
    'rb-us-6': '**************',
    'rb-us-7': '*************',
    'rb-us-8': '*************', 
    'rb-us-9': '***********',
    'rb-us-10': '*************',
    'rb-us-11': '*************',
    'rb-us-12': '***************',
    'rb-us-13': '*************', 
    'rb-us-14': '*************', 
    'rb-us-15': '**************', 
    'rb-us-16': '*************', 
    'rb-us-17': '*************', 
    'rb-us-18': '************', 
    'rb-us-19': '**************', 
    'rb-us-20': '*************', 
    'rb-us-21': '************', 
    'rb-us-22': '**************', 
    'rb-us-23': '**************', 
    'rb-us-24': '*************', 
    'rb-us-25': '*************', 
    'rb-us-26': '*************', 
    'rb-us-27': '*************', 
    'rb-us-28': '************', 
    'rb-us-29': '*************', 
    'rb-us-30': '102.129.191.91', 
    # 'rb-us-31': '166.0.204.52', 
    'rb-us-32': '216.185.53.42', 
    'rb-us-33': '65.215.31.87', 
    'rb-us-34': '88.216.213.81', 
    'rb-us-35': '89.23.81.110', 
    'rb-us-36': '109.121.26.37',
    'rb-us-37': '109.121.26.39', 
    'rb-us-38': '206.206.96.30', 
    'rb-us-39': '74.80.254.5', 
    'rb-us-40': '88.216.103.148', 
    'rb-us-41': '88.216.185.158', 
    'rb-us-42': '109.121.26.35', 
    'rb-us-43': '109.121.26.36', 
    'rb-us-44': '65.215.24.253', 
    'rb-us-45': '65.215.117.59', 
    #'rb-us-46': '168.158.34.49', 
    'rb-us-47': '65.215.60.33', 
    #'rb-us-48': '168.158.34.113', 
    #'rb-us-49': '168.158.34.97', 
    'rb-us-50': '************',
    }
index_file = "last_proxy_index.txt"
token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NzM0Y2IyZDQ5NjAwZmQxZDA2NjliYjgiLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2NzM0Y2UxZDQ5NjAwZmQxZDA2N2M0Y2MifQ.KK0KN7tWnCfIscwHYsCM7P71OEPyFUink8l3yCqCoPw"


class PlayStationAccountCreatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PlayStation Account Creator")
        
        self.gl = None
        self.browser = None
        self.identity = None
        self.proxy_name = None
        self.proxy_host = None
        
        # Asyncio event loop setup
        self.loop = asyncio.new_event_loop()
        self.thread = threading.Thread(target=self.start_loop, args=(self.loop,), daemon=True)
        self.thread.start()
        
        self.create_widgets()
        self.setup_async_handlers()
        
    def start_loop(self, loop):
        asyncio.set_event_loop(loop)
        loop.run_forever()
        
    def schedule_coroutine(self, coro, callback=None):
        future = asyncio.run_coroutine_threadsafe(coro, self.loop)
        future.add_done_callback(lambda f: self.handle_result(f, callback))
        
    def handle_result(self, future, callback):
        try:
            result = future.result()
        except Exception as e:
            self.log(f"Error: {str(e)}")
        else:
            if callback:
                self.root.after(0, callback, result)
                
    def log(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.config(state=tk.DISABLED)
        self.log_text.see(tk.END)
        
    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Browser Controls
        browser_frame = ttk.LabelFrame(main_frame, text="Browser Controls", padding=10)
        browser_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        ttk.Button(browser_frame, text="Start Browser", command=self.start_browser).pack(side=tk.LEFT, padx=5)
        ttk.Button(browser_frame, text="Close Browser", command=self.close_browser).pack(side=tk.LEFT, padx=5)

        # Identity Management
        identity_frame = ttk.LabelFrame(main_frame, text="Identity Management", padding=10)
        identity_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        
        ttk.Button(identity_frame, text="Generate Identity", command=self.generate_identity).pack(side=tk.LEFT, padx=5)
        ttk.Button(identity_frame, text="Get Verification Link", command=self.get_verification_link).pack(side=tk.LEFT, padx=5)

        # SSO Management
        sso_frame = ttk.LabelFrame(main_frame, text="SSO Management", padding=10)
        sso_frame.grid(row=2, column=0, sticky="ew", padx=5, pady=5)
        
        ttk.Button(sso_frame, text="Get SSO Code", command=self.get_sso_code).pack(side=tk.LEFT, padx=5)
        ttk.Button(sso_frame, text="Submit SSO Code", command=self.submit_sso_code).pack(side=tk.LEFT, padx=5)

        # Copy Fields
        copy_frame = ttk.LabelFrame(main_frame, text="Copy Fields", padding=10)
        copy_frame.grid(row=3, column=0, sticky="ew", padx=5, pady=5)
        
        fields = ['DoB', 'City', 'Postcode', 'Email', 'Username', 'Password', 'Firstname', 'Lastname']
        for field in fields:
            ttk.Button(copy_frame, text=f"Copy {field}", 
                      command=lambda f=field.lower(): self.copy_field(f)).pack(side=tk.LEFT, padx=2)

        # Logs
        log_frame = ttk.LabelFrame(main_frame, text="Logs", padding=10)
        log_frame.grid(row=4, column=0, sticky="nsew", padx=5, pady=5)
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=10)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(state=tk.DISABLED)

        # Exit Button
        ttk.Button(main_frame, text="Exit", command=self.exit_program).grid(row=5, column=0, pady=5)

        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)

    def setup_async_handlers(self):
        self.async_handlers = {
            'browser_started': lambda b: setattr(self, 'browser', b),
            'browser_closed': lambda _: setattr(self, 'browser', None),
            'identity_generated': lambda i: setattr(self, 'identity', i),
        }

    # Button handlers
    def start_browser(self):
        self.schedule_coroutine(self.async_start_browser(), self.handle_browser_started)

    async def async_start_browser(self):
        name, host = await self.get_proxy(False)
        print(self.identity['email'])
        profile_id = await get_one_by_email(self.identity['email'])
        print(profile_id)

        gl = GoLogin({
            "token": token,
            "profile_id": profile_id,
            })
        
        debugger_address = gl.start()
        self.gl = gl

        # proxy = {
        #     'server': f'{host}:4444',
        #     'username': "b03f165be1",
        #     'password': "w1q4cGzT"
        # }
        # antidetect = await Antidetect().start()
        # browser = await antidetect.new_browser(
        #     proxy_string=f"http://{proxy['username']}:{proxy['password']}@{proxy['server']}", 
        #     headless=False
        # )
        # page = await browser.new_page()
        # await page.set_viewport_size({"width": 1080, "height": 720})
        # await page.goto("https://playstation.com/es-mx/")
        # return browser

    async def get_proxy(self, from_identity):
        if not from_identity and self.proxy_host:
            return self.proxy_name, self.proxy_host
        
            
        if os.path.exists(index_file):
            with open(index_file, "r") as f:
                try:
                    last_index = int(f.read().strip())
                except ValueError:
                    last_index = -1
        else:
            last_index = -1
        next_index = (last_index + 1) % len(proxy_hosts)

        with open(index_file, "w") as f:
            f.write(str(next_index))

        self.proxy_name = f"rb-us-{next_index}"
        self.proxy_host = proxy_hosts.get(f"rb-us-{next_index}")
        return self.proxy_name, self.proxy_host

    def handle_browser_started(self, browser):
        self.browser = browser
        self.log("Browser started successfully")

    def close_browser(self):
        # if self.browser:
        #     self.schedule_coroutine(self.async_close_browser(), self.handle_browser_closed)
        self.gl.stop()

    async def async_close_browser(self):
        await self.browser.close()
        return True

    def handle_browser_closed(self, result):
        self.browser = None
        self.log("Browser closed successfully")

    def generate_identity(self):
        self.schedule_coroutine(self.async_generate_identity(), self.handle_identity_generated)

    async def async_generate_identity(self):
        identity = IdentityGen()
        add_user(identity['email'])
        name, host = await self.get_proxy(True)
        await generate_new_profile(identity['email'], host, name)
        return identity

    def handle_identity_generated(self, identity):
        self.identity = identity
        self.log("New identity generated:\n" + "\n".join(f"{k}: {v}" for k, v in identity.items()))

    def get_verification_link(self):
        if not self.identity:
            messagebox.showerror("Error", "Generate an identity first!")
            return
        self.schedule_coroutine(self.async_get_verification_link(), self.handle_verification_link)

    async def async_get_verification_link(self):
        link = await get_link(self.identity['email'])
        pyperclip.copy(link)
        return link

    def handle_verification_link(self, link):
        self.log(f"Verification link copied to clipboard: {link}")

    def get_sso_code(self):
        url = "https://ca.account.sony.com/api/v1/ssocookie"
        pyperclip.copy(url)
        self.log(f"SSO URL copied to clipboard: {url}")

    def submit_sso_code(self):
        if not self.identity:
            messagebox.showerror("Error", "Generate an identity first!")
            return
        sso = simpledialog.askstring("SSO Code", "Enter SSO code:")
        if sso:
            match = re.search(r'"npsso":"([^"]+)"', sso)
            if match:
                npsso_value = match.group(1)
                print(npsso_value)
                self.schedule_coroutine(self.async_submit_sso(npsso_value), self.handle_sso_submitted)
            else:
                self.log("Invalid format for sso, Please copy the whole line")

    async def async_submit_sso(self, sso):
        postgres = PostgrestClient()
        response = await postgres.register_identity(self.identity)
        await postgres.register_identity_sso(response['id'], sso)
        return True

    def handle_sso_submitted(self, result):
        self.log("SSO code submitted and account saved to accounts.csv")

    def copy_field(self, field):
        if not self.identity:
            messagebox.showerror("Error", "Generate an identity first!")
            return
        value = self.identity.get(field)
        if value:
            pyperclip.copy(value)
            self.log(f"Copied {field}: {value}")
        else:
            self.log(f"Field {field} not found in identity")

    def exit_program(self):
        if self.browser:
            self.schedule_coroutine(self.async_close_browser(), self.handle_exit)
        else:
            self.root.destroy()

    def handle_exit(self, _):
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = PlayStationAccountCreatorGUI(root)
    root.mainloop()