#!/usr/bin/env python3
"""
Test script for PlayStation account creation automation.

This script demonstrates how to use the automated account creation functionality.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auto.ps_helper.create_account_camoufox import create_account_automated, main

def test_automated_creation():
    """Test the automated account creation process."""
    print("🚀 Starting PlayStation Account Creation Test")
    print("=" * 50)
    
    try:
        # Run the automated account creation
        success = create_account_automated()
        
        if success:
            print("\n✅ SUCCESS: Account creation completed!")
            print("The account has been created and saved to the database.")
            return True
        else:
            print("\n❌ FAILED: Account creation was not successful.")
            print("Please check the logs above for error details.")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ INTERRUPTED: Account creation was stopped by user.")
        return False
    except Exception as e:
        print(f"\n💥 ERROR: An unexpected error occurred: {e}")
        return False

def main_menu():
    """Display a simple menu for testing."""
    print("PlayStation Account Creation Test")
    print("=" * 35)
    print("1. Run Automated Account Creation")
    print("2. Run Interactive Menu")
    print("3. Exit")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-3): ").strip()
            
            if choice == "1":
                test_automated_creation()
                break
            elif choice == "2":
                # Import and run the interactive menu
                import asyncio
                from auto.ps_helper.create_account_camoufox import run
                asyncio.run(run())
                break
            elif choice == "3":
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please enter 1, 2, or 3.")
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--auto":
            test_automated_creation()
        elif sys.argv[1] == "--main":
            main()
        else:
            print("Usage: python test_account_creation.py [--auto|--main]")
    else:
        main_menu()
