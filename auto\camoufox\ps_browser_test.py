"""
Script to refresh PlayStation accounts.
"""

from auto.camoufox.ps_browser import refresh_profile
from auto.camoufox.pull_ps_accounts import get_number_of_accounts, get_all_accounts
from auto.camoufox.failed_accounts_manager import add_failed_account


def main():
    print("Starting PlayStation account refresh...")
    number_of_accounts = 10
    accounts = get_number_of_accounts(number_of_accounts)

    if len(accounts) < number_of_accounts:
        print(f"Warning: Only {len(accounts)} accounts available after filtering out failed accounts")

    if not accounts:
        print("No accounts to refresh. Exiting.")
        return

    count = 1
    successes = 0
    total_accounts = len(accounts)

    for account in accounts:
        email = account.get('email', 'unknown')
        print(f"Refreshing account {count} of {total_accounts}: {email}")

        try:
            success = refresh_profile(account)

            if success:
                successes += 1
                print(f"✅ Account {email} refreshed successfully")
            else:
                add_failed_account(email)
                print(f"❌ Account {email} failed to refresh and has been added to the failed accounts list")
        except Exception as e:
            error_message = str(e)
            # Check if it's a proxy-related error
            if any(term in error_message.lower() for term in [
                'proxy', 'connection', 'network', 'timeout', 'socket',
                'dns', 'connect', 'unreachable', 'refused', 'reset'
            ]):
                print(f"⚠️ Account {email} encountered a proxy error - skipping without marking as failed")
            else:
                print(f"⚠️ Account {email} failed with unexpected error: {error_message}")

        count += 1
        print(f"{successes}/{count-1} accounts refreshed successfully so far")

    print(f"\nSummary: {successes}/{total_accounts} accounts refreshed successfully")


if __name__ == "__main__":
    main()
