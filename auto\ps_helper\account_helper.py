import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import asyncio
import threading
import pyperclip
import random
import re
import csv
from mail_util import add_user
from create_identity import IdentityGen
from get_email_hyperlink import get_link
from antidetect.antidetect_browser import Antidetect
from postgres import PostgrestClient
import os

proxy_list = [
    "*************",
    "**************",
    "*************",
    "*************",
    "***********",
    "**************",
    "*************",
    "*************",
    "***********",
    "*************",
    "*************",
    "***************",
    "*************",
    "*************",
    "**************",
    "*************",
    "*************",
    "************",
    "**************",
    "*************",
    "************",
    "**************",
    "**************",
    "*************",
    "*************",
    "*************",
    "*************",
    "************",
    "*************",
    "**************",
    "************",
    "*************",
    "************",
    "*************",
    "************",
    "109.121.26.37",
    "109.121.26.39",
    "206.206.96.30",
    "74.80.254.5",
    "88.216.103.148",
    "88.216.185.158",
    "109.121.26.35",
    "109.121.26.36",
    "65.215.24.253",
    "65.215.117.59",
    "168.158.34.49",
    "65.215.60.33",
    "168.158.34.113",
    "168.158.34.97",
    "65.215.60.25",
]
index_file = "last_proxy_index.txt"

class PlayStationAccountCreatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PlayStation Account Creator")
        
        self.browser = None
        self.identity = None
        
        # Asyncio event loop setup
        self.loop = asyncio.new_event_loop()
        self.thread = threading.Thread(target=self.start_loop, args=(self.loop,), daemon=True)
        self.thread.start()
        
        self.create_widgets()
        self.setup_async_handlers()
        
    def start_loop(self, loop):
        asyncio.set_event_loop(loop)
        loop.run_forever()
        
    def schedule_coroutine(self, coro, callback=None):
        future = asyncio.run_coroutine_threadsafe(coro, self.loop)
        future.add_done_callback(lambda f: self.handle_result(f, callback))
        
    def handle_result(self, future, callback):
        try:
            result = future.result()
        except Exception as e:
            self.log(f"Error: {str(e)}")
        else:
            if callback:
                self.root.after(0, callback, result)
                
    def log(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.config(state=tk.DISABLED)
        self.log_text.see(tk.END)
        
    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Browser Controls
        browser_frame = ttk.LabelFrame(main_frame, text="Browser Controls", padding=10)
        browser_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        ttk.Button(browser_frame, text="Start Browser", command=self.start_browser).pack(side=tk.LEFT, padx=5)
        ttk.Button(browser_frame, text="Close Browser", command=self.close_browser).pack(side=tk.LEFT, padx=5)

        # Identity Management
        identity_frame = ttk.LabelFrame(main_frame, text="Identity Management", padding=10)
        identity_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        
        ttk.Button(identity_frame, text="Generate Identity", command=self.generate_identity).pack(side=tk.LEFT, padx=5)
        ttk.Button(identity_frame, text="Get Verification Link", command=self.get_verification_link).pack(side=tk.LEFT, padx=5)

        # SSO Management
        sso_frame = ttk.LabelFrame(main_frame, text="SSO Management", padding=10)
        sso_frame.grid(row=2, column=0, sticky="ew", padx=5, pady=5)
        
        ttk.Button(sso_frame, text="Get SSO Code", command=self.get_sso_code).pack(side=tk.LEFT, padx=5)
        ttk.Button(sso_frame, text="Submit SSO Code", command=self.submit_sso_code).pack(side=tk.LEFT, padx=5)

        # Copy Fields
        copy_frame = ttk.LabelFrame(main_frame, text="Copy Fields", padding=10)
        copy_frame.grid(row=3, column=0, sticky="ew", padx=5, pady=5)
        
        fields = ['DoB', 'City', 'Postcode', 'Email', 'Username', 'Password', 'Firstname', 'Lastname']
        for field in fields:
            ttk.Button(copy_frame, text=f"Copy {field}", 
                      command=lambda f=field.lower(): self.copy_field(f)).pack(side=tk.LEFT, padx=2)

        # Logs
        log_frame = ttk.LabelFrame(main_frame, text="Logs", padding=10)
        log_frame.grid(row=4, column=0, sticky="nsew", padx=5, pady=5)
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=10)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(state=tk.DISABLED)

        # Exit Button
        ttk.Button(main_frame, text="Exit", command=self.exit_program).grid(row=5, column=0, pady=5)

        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)

    def setup_async_handlers(self):
        self.async_handlers = {
            'browser_started': lambda b: setattr(self, 'browser', b),
            'browser_closed': lambda _: setattr(self, 'browser', None),
            'identity_generated': lambda i: setattr(self, 'identity', i),
        }

    # Button handlers
    def start_browser(self):
        self.schedule_coroutine(self.async_start_browser(), self.handle_browser_started)

    async def async_start_browser(self):
        if os.path.exists(index_file):
            with open(index_file, "r") as f:
                try:
                    last_index = int(f.read().strip())
                except ValueError:
                    last_index = -1
        else:
            last_index = -1
        next_index = (last_index + 1) % len(proxy_list)

        with open(index_file, "w") as f:
            f.write(str(next_index))
        host = proxy_list[next_index]
        print(next_index)

        proxy = {
            'server': f'{host}:4444',
            'username': "b03f165be1",
            'password': "w1q4cGzT"
        }
        antidetect = await Antidetect().start()
        browser = await antidetect.new_browser(
            proxy_string=f"http://{proxy['username']}:{proxy['password']}@{proxy['server']}", 
            headless=False
        )
        page = await browser.new_page()
        await page.set_viewport_size({"width": 1080, "height": 720})
        await page.goto("https://playstation.com/es-mx/")
        return browser

    def handle_browser_started(self, browser):
        self.browser = browser
        self.log("Browser started successfully")

    def close_browser(self):
        if self.browser:
            self.schedule_coroutine(self.async_close_browser(), self.handle_browser_closed)

    async def async_close_browser(self):
        await self.browser.close()
        return True

    def handle_browser_closed(self, result):
        self.browser = None
        self.log("Browser closed successfully")

    def generate_identity(self):
        self.schedule_coroutine(self.async_generate_identity(), self.handle_identity_generated)

    async def async_generate_identity(self):
        identity = IdentityGen()
        add_user(identity['email'])
        return identity

    def handle_identity_generated(self, identity):
        self.identity = identity
        self.log("New identity generated:\n" + "\n".join(f"{k}: {v}" for k, v in identity.items()))

    def get_verification_link(self):
        if not self.identity:
            messagebox.showerror("Error", "Generate an identity first!")
            return
        self.schedule_coroutine(self.async_get_verification_link(), self.handle_verification_link)

    async def async_get_verification_link(self):
        link = await get_link(self.identity['email'])
        pyperclip.copy(link)
        return link

    def handle_verification_link(self, link):
        self.log(f"Verification link copied to clipboard: {link}")

    def get_sso_code(self):
        url = "https://ca.account.sony.com/api/v1/ssocookie"
        pyperclip.copy(url)
        self.log(f"SSO URL copied to clipboard: {url}")

    def submit_sso_code(self):
        if not self.identity:
            messagebox.showerror("Error", "Generate an identity first!")
            return
        sso = simpledialog.askstring("SSO Code", "Enter SSO code:")
        if sso:
            match = re.search(r'"npsso":"([^"]+)"', sso)
            if match:
                npsso_value = match.group(1)
                print(npsso_value)
                self.schedule_coroutine(self.async_submit_sso(npsso_value), self.handle_sso_submitted)
            else:
                self.log("Invalid format for sso, Please copy the whole line")

    async def async_submit_sso(self, sso):
        postgres = PostgrestClient()
        response = await postgres.register_identity(self.identity)
        await postgres.register_identity_sso(response['id'], sso)
        return True

    def handle_sso_submitted(self, result):
        self.log("SSO code submitted and account saved to accounts.csv")


    def copy_field(self, field):
        if not self.identity:
            messagebox.showerror("Error", "Generate an identity first!")
            return
        value = self.identity.get(field)
        if value:
            pyperclip.copy(value)
            self.log(f"Copied {field}: {value}")
        else:
            self.log(f"Field {field} not found in identity")

    def exit_program(self):
        if self.browser:
            self.schedule_coroutine(self.async_close_browser(), self.handle_exit)
        else:
            self.root.destroy()

    def handle_exit(self, _):
        self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = PlayStationAccountCreatorGUI(root)
    root.mainloop()