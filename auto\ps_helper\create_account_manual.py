from auto.ps_helper.mail_util import add_user
from auto.ps_helper.create_identity import IdentityGen
from auto.ps_helper.get_email_hyperlink import get_link
from auto.ps_helper.postgres import PostgrestClient
from pprint import pprint
import random
import threading
import re
import pyperclip
import csv
import os
import json
import time
from pathlib import Path
from typing import Dict, Optional, Any
from camoufox import Async<PERSON><PERSON><PERSON><PERSON>, launch_options
from auto.camoufox.proxy_manager import get_proxy
import sys
import asyncio
from anticaptchaofficial.funcaptchaproxyon import *

if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

def opts_path(name: str) -> str:
    return f"auto/camoufox/profiles/{name}/opts.json"

def config_exists(name: str) -> bool:
    return Path(opts_path(name)).exists()

def generate_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Generate a new profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        New profile configuration dictionary
    """
    profile_dir = os.path.dirname(opts_path(name))

    try:
        # Use the same addon path as in your manifest
        addon_path = os.path.abspath("auto/ps_helper/anticaptcha-plugin")
        
        if os.path.exists(addon_path):
            manifest_path = os.path.join(addon_path, "manifest.json")

        opts = launch_options(
            user_data_dir=profile_dir,
            os=['windows'],
            proxy=proxy,
            geoip=True,
            window=(1080, 720),
            humanize=True,
            headless=False,
            # addons=[addon_path] if os.path.exists(addon_path) else []
        )

        os.makedirs(profile_dir, exist_ok=True)
        with open(opts_path(name), "w") as opts_file:
            json.dump(opts, opts_file, indent=4)
        return opts

    except Exception as e:
        print(f"Error generating profile: {e}")
        raise

def get_profile(name: str, proxy: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """Get or create a profile configuration.

    Args:
        name: Profile name
        proxy: Optional proxy configuration

    Returns:
        Profile configuration dictionary
    """
    if config_exists(name=name):
        with open(opts_path(name)) as opts_file:
            return json.load(opts_file)
    return generate_profile(name, proxy)

async def generate_identity():
    identity = IdentityGen()
    add_user(identity['email'])
    return identity

async def get_verification_email(email):
    link = await get_link(email)
    pyperclip.copy(link)
    return link

async def start_browser():
    proxy = get_proxy()
    
    # Generate a unique profile name
    profile_name = f"ps_account_{int(time.time())}"
    
    # Get profile options with proxy
    profile_opts = get_profile(profile_name, proxy)
    
    # Launch CamouFox browser
    try:
        browser = await AsyncCamoufox(from_options=profile_opts, persistent_context=True, headless=False).__aenter__()
        page = await browser.new_page()
        await page.goto("https://playstation.com/es-mx/")
        return browser, page
    except Exception as e:
        print(f"Error launching browser: {e}")
        raise


async def close_browser(browser_data):
    browser, page = browser_data
    await browser.close()

async def submit_sso(identity:dict):
    sso = await async_input("\nEnter sso: ")
    if sso:
        match = re.search(r'"npsso":"([^"]+)"', sso)
        if match:
            npsso_value = match.group(1)
            postgres = PostgrestClient()
            response = await postgres.register_identity(identity)
            print(response)
            await postgres.register_identity_sso(response['id'], npsso_value)


async def copy_handler(info_required, identity):
    print(identity[info_required])
    pyperclip.copy(identity[info_required])

async def async_input(prompt):
    return await asyncio.get_event_loop().run_in_executor(None, input, prompt)

async def show_menu(options, title=None):
    print()
    if title:
        print(title)
    for i, option in enumerate(options, 1):
        print(f"{i}: {option}")
    
    while True:
        try:
            choice = int(await async_input("\nEnter choice: "))
            if 1 <= choice <= len(options):
                return options[choice-1]
            print(f"Please enter 1-{len(options)}")
        except ValueError:
            print("Please enter a number")

async def run():
    browser_data = None
    identity = None
    finished = False
    while not finished:
        action = await show_menu(["Start Browser", "Close Browser", "Generate Identity", "Get Verification Link", "Get SSO Code",
                                   "Submit SSO Code", "Debug Captcha State", "Copy DoB", "Copy City", "Copy Postcode", "Copy Email", "Copy Username",
                                    "Copy Password", "Copy Firstname", "Copy Lastname", "End Program"])
        if action == "Start Browser":
            if browser_data is None:
                browser_data = await start_browser()
                print("CamouFox browser started.")
            else:
                print("Browser is already running.")
        if action == "Close Browser":
            if browser_data:
                await close_browser(browser_data)
                browser_data = None
                print("Browser closed.")
            else:
                print("No Browser to close.")
        if action == "Generate Identity":
            identity = await generate_identity()
            print(identity)
        if action == "Get Verification Link":
            if identity != None:
                await get_verification_email(identity['email'])
            else:
                print("Haven't Created An Identity Yet")
        if action == "Get SSO Code":
            print("https://ca.account.sony.com/api/v1/ssocookie")
            pyperclip.copy("https://ca.account.sony.com/api/v1/ssocookie")
        if action == "Submit SSO Code":
            await submit_sso(identity)
        if action == "Debug Captcha State":
            await funcaptcha_state(browser_data)
        if re.match(r'Copy', action):
            if identity != None:
                await copy_handler(" ".join(action.split(" ")[1:]).lower(), identity)
        if action == "End Program":
            finished = True
            if browser_data != None:
                await close_browser(browser_data)

async def funcaptcha_state(browser_data):
    """
    Debug and solve FunCaptcha using 2captcha library.

    Args:
        browser_data: Tuple of (browser, page) from the browser session
    """
    from twocaptcha import TwoCaptcha
    import json
    import glob
    import os

    if not browser_data:
        print("No browser session available")
        return

    _, page = browser_data
    api_key = "5f91440e6517b4c4b4bf8cb5d7b79ea6"

    try:
        print("=== FUNCAPTCHA DEBUG STATE ===")

        # Extract FunCaptcha information from the page
        captcha_info = await page.evaluate('''
            () => {
                const info = {};

                // Look for FunCaptcha elements
                const funcaptchaIframe = document.querySelector('iframe[src*="funcaptcha"], iframe[src*="arkoselabs"]');
                if (funcaptchaIframe) {
                    info.iframeFound = true;
                    info.iframeSrc = funcaptchaIframe.src;
                } else {
                    info.iframeFound = false;
                }

                // Look for data-sitekey or public key
                const siteKeyElements = document.querySelectorAll('[data-sitekey], [data-public-key], [data-pk]');
                if (siteKeyElements.length > 0) {
                    info.siteKeyFound = true;
                    info.siteKey = siteKeyElements[0].getAttribute('data-sitekey') ||
                                   siteKeyElements[0].getAttribute('data-public-key') ||
                                   siteKeyElements[0].getAttribute('data-pk');
                } else {
                    info.siteKeyFound = false;
                }

                // Look for FunCaptcha specific elements
                const funcaptchaElements = document.querySelectorAll('[id*="funcaptcha"], [class*="funcaptcha"], [data-callback*="funcaptcha"]');
                info.funcaptchaElementsCount = funcaptchaElements.length;

                // Check for any captcha buttons or triggers
                const captchaButtons = document.querySelectorAll('button[data-theme*="verify"], button[aria-label*="captcha"], button[aria-label*="puzzle"]');
                info.captchaButtonsCount = captchaButtons.length;

                // Get current URL
                info.currentUrl = window.location.href;

                // Check for any script tags that might contain FunCaptcha configuration
                const scripts = Array.from(document.querySelectorAll('script')).map(script => script.textContent || script.src);
                const funcaptchaScripts = scripts.filter(script => script && (script.includes('funcaptcha') || script.includes('arkoselabs')));
                info.funcaptchaScriptsCount = funcaptchaScripts.length;

                return info;
            }
        ''')

        print(f"Current URL: {captcha_info.get('currentUrl', 'N/A')}")
        print(f"FunCaptcha iframe found: {captcha_info.get('iframeFound', False)}")
        if captcha_info.get('iframeSrc'):
            print(f"Iframe src: {captcha_info.get('iframeSrc')}")
        print(f"Site key found: {captcha_info.get('siteKeyFound', False)}")
        if captcha_info.get('siteKey'):
            print(f"Site key: {captcha_info.get('siteKey')}")
        print(f"FunCaptcha elements: {captcha_info.get('funcaptchaElementsCount', 0)}")
        print(f"Captcha buttons: {captcha_info.get('captchaButtonsCount', 0)}")
        print(f"FunCaptcha scripts: {captcha_info.get('funcaptchaScriptsCount', 0)}")

        # If we found a site key, attempt to solve the captcha
        if captcha_info.get('siteKeyFound') and captcha_info.get('siteKey'):
            site_key = captcha_info.get('siteKey')
            url = captcha_info.get('currentUrl', 'https://playstation.com/es-mx/')

            print(f"\n=== ATTEMPTING TO SOLVE FUNCAPTCHA ===")
            print(f"Site key: {site_key}")
            print(f"URL: {url}")

            # Create 2captcha solver
            solver = TwoCaptcha(api_key)

            # Get proxy information from CamouFox profile opts.json
            proxy_dict = None
            try:
                proxy_config = None

                # Find the most recent profile opts.json file
                profile_pattern = "auto/camoufox/profiles/ps_account_*/opts.json"
                opts_files = glob.glob(profile_pattern)

                if opts_files:
                    # Get the most recent opts.json file
                    latest_opts_file = max(opts_files, key=os.path.getmtime)
                    print(f"Reading proxy from: {latest_opts_file}")

                    with open(latest_opts_file, 'r') as f:
                        opts_data = json.load(f)

                    if 'proxy' in opts_data and opts_data['proxy']:
                        proxy_config = opts_data['proxy']
                        print(f"Found proxy config: {proxy_config}")

                if proxy_config and proxy_config.get('server'):
                    # Parse proxy server (remove protocol if present)
                    server = proxy_config.get('server', '').replace('http://', '').replace('https://', '')
                    if ':' in server:
                        proxy_address, proxy_port = server.split(':', 1)
                        # Format proxy for 2captcha
                        proxy_dict = {
                            'proxytype': 'HTTP',
                            'proxy': f"{proxy_address}:{proxy_port}",
                            'proxylogin': proxy_config.get('username', ''),
                            'proxypassword': proxy_config.get('password', '')
                        }
                        print(f"Using CamouFox profile proxy: {proxy_address}:{proxy_port}")
                    else:
                        print(f"Invalid proxy format: {server}")
                else:
                    print("No proxy configuration found in CamouFox profile opts.json")

            except Exception as e:
                print(f"Could not read proxy from opts.json: {e}")
                print("Continuing without proxy...")

            # Get user agent from the page
            user_agent = await page.evaluate('() => navigator.userAgent')
            print(f"User agent: {user_agent}")

            print("Submitting task to 2captcha service...")

            # Prepare funcaptcha parameters
            funcaptcha_params = {
                'sitekey': site_key,
                'url': url,
                'useragent': user_agent
            }

            # Add proxy if available
            if proxy_dict:
                funcaptcha_params.update(proxy_dict)
                print("Using proxy for 2captcha")

            # Optional: Add surl if needed (Arkose Labs subdomain)
            # funcaptcha_params['surl'] = 'https://client-api.arkoselabs.com'

            try:
                result = solver.funcaptcha(**funcaptcha_params)
                token = result.get('code')

                if token:
                    print(f"\n=== CAPTCHA SOLVED SUCCESSFULLY ===")
                    print(f"Solution token: {token}")

                    # Try to inject the solution into the page
                    try:
                        injection_result = await page.evaluate(f'''
                            () => {{
                                // Look for FunCaptcha response field
                                const responseField = document.querySelector('input[name="fc-token"], textarea[name="fc-token"], input[id*="funcaptcha"], textarea[id*="funcaptcha"]');
                                if (responseField) {{
                                    responseField.value = '{token}';
                                    responseField.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                    console.log('FunCaptcha token injected successfully');
                                    return true;
                                }}
                                return false;
                            }}
                        ''')

                        if injection_result:
                            print("Token injected into page successfully!")
                        else:
                            print("Could not find response field to inject token")
                            print("You may need to manually paste the token")

                    except Exception as e:
                        print(f"Could not inject token into page: {e}")
                        print("You may need to manually paste the token")
                else:
                    print("Failed to solve captcha - no token received")

            except Exception as solve_error:
                print(f"Failed to solve captcha. Error: {solve_error}")

        else:
            print("\nNo FunCaptcha site key found on current page")
            print("Make sure you're on a page with an active FunCaptcha challenge")

    except Exception as e:
        print(f"Error in funcaptcha_state: {e}")
        import traceback
        traceback.print_exc()

    print("=== END FUNCAPTCHA DEBUG ===\n")


if __name__ == "__main__":
    import asyncio
    asyncio.run(run())


