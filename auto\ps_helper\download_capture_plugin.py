import urllib.request
import zipfile
import os
from pathlib import Path

# Get the directory where this script is located
script_dir = Path(__file__).parent

url = "https://antcpt.com/downloads/firefox/anticaptcha-plugin_v0.60.xpi"
filehandle, _ = urllib.request.urlretrieve(url)

# Extract to a subdirectory in the same location as the script
extract_path = script_dir / "anticaptcha-plugin"
with zipfile.ZipFile(filehandle, 'r') as zip_ref:
    zip_ref.extractall(extract_path)

api_key = "5f91440e6517b4c4b4bf8cb5d7b79ea6"
config_file = extract_path / "js" / "config_ac_api_key.js"
config_file.write_text(config_file.read_text().replace("antiCapthaPredefinedApiKey = ''", f"antiCapthaPredefinedApiKey = '{api_key}'"))

# Create the final zip in the same directory as the script
zip_path = script_dir / "anticaptcha-plugin.zip"
zip_file = zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED)
for root, dirs, files in os.walk(extract_path):
    for file in files:
        file_path = os.path.join(root, file)
        arcname = os.path.relpath(file_path, extract_path)
        zip_file.write(file_path, arcname=arcname)
zip_file.close()
