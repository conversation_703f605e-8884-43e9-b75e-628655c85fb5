(function(){var i=100;var d=5e3;var r=false;var l=[];var s=[];window.postMessagePosteRestante=function(e,a,s,n){r&&console.log("Post message Poste Restante init",a,window?window.location.href:"");var o={__receiver:e,__messageId:Math.random()};o=Object.assign(a,o);var t=setInterval(function(){r&&console.log("Sending original message",o);window.postMessage.call(this,o,s,n)},i);l[o.__messageId]=t;setTimeout(function(){if(typeof l[o.__messageId]!=="undefined"){r&&console.log("Clearing interval by timeout for message",o.__messageId);clearInterval(l[o.__messageId]);delete l[o.__messageId]}},d);r&&console.log("messagePostingIntervals",l)};window.receiveMessagePosteRestante=function(e,a){r&&console.log("Subscribing receiver",e,window?window.location.href:"");if(typeof s[e]==="undefined"){s[e]=[]}s[e].push(a);r&&console.log("receiverCallbacks",s)};window.addEventListener("message",function(e){r&&console.log("Poste Restante incoming event",e);if(e.data&&typeof e.data.__receiver!=="undefined"&&typeof e.data.__messageId!=="undefined"){r&&console.log("It's an Original message for",e.data.__receiver);if(typeof s[e.data.__receiver]!=="undefined"){r&&console.log("Receiver exists, calling callbacks");for(var a in s[e.data.__receiver]){if(typeof s[e.data.__receiver][a]==="function"){s[e.data.__receiver][a](e)}}r&&console.log("Sending a Confirmation message for",e.data.__receiver);e.source.postMessage({__messageId:e.data.__messageId},e.origin)}else{r&&console.log("Receiver does not exist")}return}if(e.data&&typeof e.data.__messageId!=="undefined"){r&&console.log("It's a Confirmation message, clearing an interval");if(typeof l[e.data.__messageId]!=="undefined"){clearInterval(l[e.data.__messageId]);delete l[e.data.__messageId]}}})})();