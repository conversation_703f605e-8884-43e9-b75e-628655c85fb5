"""Test script for PlayStation account creation automation.

This script demonstrates how to use the ps_account_creator module
to create PlayStation accounts automatically.
"""

from ps_account_creator import create_playstation_account, IdentityGen
from auto.ps_helper.mail_util import add_user
import time


def test_identity_generation():
    """Test the identity generation functionality."""
    print("Testing identity generation...")
    
    try:
        identity = IdentityGen()
        print("✅ Identity generated successfully!")
        print(f"Email: {identity['email']}")
        print(f"Name: {identity['name']['first_name']} {identity['name']['last_name']}")
        print(f"DOB: {identity['dob']}")
        print(f"City: {identity['city']}")
        print(f"Province: {identity['province']}")
        print(f"Postcode: {identity['postcode']}")
        print(f"Username: {identity['username']}")
        print(f"Password: {identity['password']}")
        return True
    except Exception as e:
        print(f"❌ Identity generation failed: {e}")
        return False


def test_email_setup():
    """Test the email account setup."""
    print("\nTesting email setup...")
    
    try:
        identity = IdentityGen()
        add_user(identity['email'])
        print(f"✅ Email account created: {identity['email']}")
        return True
    except Exception as e:
        print(f"❌ Email setup failed: {e}")
        return False


def create_single_account():
    """Create a single PlayStation account."""
    print("\n" + "="*60)
    print("CREATING SINGLE PLAYSTATION ACCOUNT")
    print("="*60)
    
    success = create_playstation_account()
    
    if success:
        print("\n🎉 Account creation completed successfully!")
    else:
        print("\n💥 Account creation failed!")
    
    return success


def create_multiple_accounts(count: int = 3):
    """Create multiple PlayStation accounts."""
    print(f"\n" + "="*60)
    print(f"CREATING {count} PLAYSTATION ACCOUNTS")
    print("="*60)
    
    successful_accounts = 0
    failed_accounts = 0
    
    for i in range(count):
        print(f"\n--- Creating Account {i+1}/{count} ---")
        
        success = create_playstation_account()
        
        if success:
            successful_accounts += 1
            print(f"✅ Account {i+1} created successfully!")
        else:
            failed_accounts += 1
            print(f"❌ Account {i+1} creation failed!")
        
        # Wait between account creations to avoid rate limiting
        if i < count - 1:
            print("Waiting 30 seconds before next account...")
            time.sleep(30)
    
    print(f"\n" + "="*60)
    print("BATCH CREATION SUMMARY")
    print("="*60)
    print(f"✅ Successful: {successful_accounts}")
    print(f"❌ Failed: {failed_accounts}")
    print(f"📊 Success Rate: {(successful_accounts/count)*100:.1f}%")


def interactive_menu():
    """Interactive menu for testing different functionalities."""
    while True:
        print("\n" + "="*50)
        print("PLAYSTATION ACCOUNT CREATOR - TEST MENU")
        print("="*50)
        print("1. Test Identity Generation")
        print("2. Test Email Setup")
        print("3. Create Single Account")
        print("4. Create Multiple Accounts (3)")
        print("5. Create Multiple Accounts (5)")
        print("6. Create Multiple Accounts (Custom)")
        print("0. Exit")
        print("-"*50)
        
        try:
            choice = input("Enter your choice (0-6): ").strip()
            
            if choice == "0":
                print("Goodbye!")
                break
            elif choice == "1":
                test_identity_generation()
            elif choice == "2":
                test_email_setup()
            elif choice == "3":
                create_single_account()
            elif choice == "4":
                create_multiple_accounts(3)
            elif choice == "5":
                create_multiple_accounts(5)
            elif choice == "6":
                try:
                    count = int(input("Enter number of accounts to create: "))
                    if count > 0:
                        create_multiple_accounts(count)
                    else:
                        print("Please enter a positive number.")
                except ValueError:
                    print("Please enter a valid number.")
            else:
                print("Invalid choice. Please try again.")
                
        except KeyboardInterrupt:
            print("\n\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")


def main():
    """Main function."""
    print("PlayStation Account Creator - Test Script")
    print("This script helps test the account creation functionality.")
    print("\nIMPORTANT NOTES:")
    print("- Make sure you have a stable internet connection")
    print("- Captchas will require manual intervention")
    print("- Email verification may take a few minutes")
    print("- The script will pause for manual captcha solving")
    
    try:
        interactive_menu()
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()
