var __hcaptchaInitParameters={responses:{lastSolution:null}};(function(){var t={};if(document.currentScript&&document.currentScript.dataset&&document.currentScript.dataset["parameters"]){try{t=JSON.parse(document.currentScript.dataset["parameters"])}catch(t){}}if(t.originalHcaptchaApiUrl&&t.currentHcaptchaApiUrl&&t.originalHcaptchaApiUrl!==t.currentHcaptchaApiUrl){var a=document.getElementsByTagName("script");for(var e in a){if(a[e].src===t.originalHcaptchaApiUrl){a[e].src=t.currentHcaptchaApiUrl;break}}}else{}var r=t.currentOnloadMethodName;var n=t.originalOnloadMethodName;if(r){function c(){var e;if(typeof window[r]==="function"){e=window[r]}window[r]=function(){var n=hcaptcha.render;var t=hcaptcha.execute;hcaptcha.render=function(t,a){if(a&&typeof a.callback=="function"){var e=a.callback;a.callback=function(){e.apply(this,arguments)}}Object.assign(__hcaptchaInitParameters,a);var r=n.apply(this,arguments);return r};var a=hcaptcha.getResponse;hcaptcha.getResponse=function(t){if(typeof __hcaptchaInitParameters["responses"][t]!=="undefined"){return __hcaptchaInitParameters["responses"][t]}else if(__hcaptchaInitParameters["responses"]["lastSolution"]){return __hcaptchaInitParameters["responses"]["lastSolution"]}else if(typeof a==="function"){return a.apply(this,arguments)}};hcaptcha.execute=function(){t.apply(this,arguments)};if(typeof e==="function"){e.apply(this,arguments)}}}if(!n||typeof window[r]!=="undefined"){c()}else{var i=setInterval(function(){if(typeof window[r]==="undefined"){return}clearInterval(i);c()},1)}}})();