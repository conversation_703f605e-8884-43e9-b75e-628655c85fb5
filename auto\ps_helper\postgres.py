import aiohttp
import logging
import datetime
import traceback
import uuid


GA_PGREST_API_URL='https://ga-pgrest.ampere-db.com'
GA_PGREST_API_JWT="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoicHNfYWNjb3VudHNfdXNlciJ9.tORD_FKjTxN1xocrdbtsqMi6v4p7XkMt1_0Q_bqP_Cw"


logger = logging.getLogger()


class PostgrestClient:
    HEADERS = {
        "Authorization": f"Bearer {GA_PGREST_API_JWT}",
        "Prefer": "return=representation",
    }

    def __init__(self, user_id: str = str(uuid.getnode())):
        self.user_id = user_id

    @staticmethod
    def current_datetime():
        current_datetime = datetime.datetime.now(datetime.timezone.utc)
        return current_datetime.isoformat()

    async def make_api_request(self, method, url, params=None, body=None, headers:dict=None):
        try:
            if headers is not None:
                self.HEADERS.update(headers)
            headers = self.HEADERS
            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(verify_ssl=False)) as session:
                async with session.request(
                    method, url, params=params, json=body, headers=headers
                ) as response:
                    response_data = await response.json()
                    response.raise_for_status()
                    return response_data
        except Exception as e:
            try:
                # try print response text
                logger.error(
                    f"Error in make_api_request: {e}, traceback: {traceback.format_exc()}"
                )
                raise
            except NameError:
                # there can be an exception in case response_text wasn't created
                pass

    async def register_identity(self, identity: dict):
        url = f"{GA_PGREST_API_URL}/accounts_identity"
        result = await self.make_api_request('POST', url, body=identity)
        return result[0]

    async def register_identity_sso(self, account_id: int, sso: str):
        url = f"{GA_PGREST_API_URL}/accounts_sso"
        body = {
            'account_id': account_id,
            'sso': sso
        }
        headers = {'Prefer': 'resolution=merge-duplicates, return=representation'}
        return await self.make_api_request('POST', url, body=body, headers=headers)
    
    async def get_identity_sso(self):
        url = f"{GA_PGREST_API_URL}/accounts_sso"
        return await self.make_api_request('GET', url)

    async def get_all_accounts_to_refresh(self):
        url = f"{GA_PGREST_API_URL}/accounts_to_refresh_joined"
        return await self.make_api_request("GET", url)
    
    async def get_all_accounts(self):
        url = f"{GA_PGREST_API_URL}/accounts_identity"
        return await self.make_api_request("GET", url)
    
    async def get_all_accounts_with_age(self):
        url = f"{GA_PGREST_API_URL}/accounts_refresh"
        return await self.make_api_request("GET", url)
    
    async def account_blocked(self, account_id: int):
        url = f"{GA_PGREST_API_URL}/accounts_blocked"
        return await self.make_api_request("POST", url, body={'account_id': account_id, 'blocked_on': self.current_datetime()})
    
    async def account_unblocked(self, account_id: int):
        url = f"{GA_PGREST_API_URL}/accounts_blocked"
        return await self.make_api_request("DELETE", url, params={'account_id': f'eq.{account_id}'})
    
    async def check_account_blocked(self, account_id: int):
        url = f"{GA_PGREST_API_URL}/accounts_blocked"
        return await self.make_api_request("GET", url, params={'account_id':  f'eq.{account_id}'})

    async def claim_account(self, account_id):
        url = f"{GA_PGREST_API_URL}/accounts_to_refresh"
        payload = {"taken_at": self.current_datetime(), "taken_by": self.user_id}
        params = {
            "account_id": f"eq.{account_id}",
            "taken_at": "is.null",
            "taken_by": "is.null",
        }

        claimed = await self.make_api_request("PATCH", url, params, payload)
        if not claimed:
            # check if account was previously claimed by us
            params = {
                "account_id": f"eq.{account_id}",
                "taken_by": f"eq.{self.user_id}",
            }

            return await self.make_api_request("GET", url, params)
        else:
            return True

    async def unclaim_account(self, account_id):
        url = f"{GA_PGREST_API_URL}/accounts_to_refresh"
        payload = {"taken_at": None, "taken_by": None}
        params = {
            "account_id": f"eq.{account_id}",
            "taken_by": f"eq.{self.user_id}",
        }

        return await self.make_api_request("PATCH", url, params, payload)

    async def update_sso(self, account_id, sso):
        url = f"{GA_PGREST_API_URL}/accounts_sso"
        payload = {"sso": sso, "updated_at": self.current_datetime()}
        params = {"account_id": f"eq.{account_id}"}

        return await self.make_api_request("PATCH", url, params, payload)

    async def delete_from_accounts_to_refresh(self, account_id):
        url = f"{GA_PGREST_API_URL}/accounts_to_refresh"
        params = {"account_id": f"eq.{account_id}"}

        return await self.make_api_request("DELETE", url, params)

postgrest_client = PostgrestClient()