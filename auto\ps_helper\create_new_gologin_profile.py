import requests
import random

all_font_list = open("fonts.txt").readlines()
proxy_list = ["es-iproyal-mobile-1", "es-iproyal-mobile-2", "es-iproyal-mobile-3"]
ram_list = [4, 8]
cpu_thread_list = [2, 4, 6, 8, 12]
vendor_list = ['Google Inc. (NVIDIA)', 'Google Inc. (Intel)', 'Google Inc. (AMD)']
nvidia = ['ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 (0x00001C20) Direct3D11 vs_5_0 ps_5_0, D3D11)', 
          'ANGLE (NVIDIA, NVIDIA GeForce GT 710 (0x0000128B) Direct3D11 vs_5_0 ps_5_0, D3D11)',
          'ANGLE (NVIDIA, NVIDIA Quadro K2200 (0x000013BA) Direct3D11 vs_5_0 ps_5_0, D3D11)',
          'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB (0x00001C03) Direct3D11 vs_5_0 ps_5_0, D3D11)',
          'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 3GB (0x00001C02) Direct3D11 vs_5_0 ps_5_0, D3D11)']

intel = ['ANGLE (Intel, Intel(R) HD Graphics 5500 (0x00001616) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) HD Graphics 520 (0x00001916) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) UHD Graphics 770 (0x00004680) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) Iris(R) Xe Graphics (0x00009A49) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) Iris(R) Xe Graphics (0x0000A7A1) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) UHD Graphics 620 (0x00003EA0) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) HD Graphics Family (0x00000A16) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) UHD Graphics 620 (0x00005917) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) HD Graphics 4600 (0x00000412) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) UHD Graphics 600 (0x00003185) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) Iris(R) Xe Graphics (0x000046A6) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) UHD Graphics (0x00008A56) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) Iris(R) Xe Graphics (0x0000A7A0) Direct3D11 vs_5_0 ps_5_0, D3D11)',
         'ANGLE (Intel, Intel(R) UHD Graphics (0x00009B41) Direct3D11 vs_5_0 ps_5_0, D3D11)']

amd = ['ANGLE (AMD, AMD Radeon RX 7600 (0x00007480) Direct3D11 vs_5_0 ps_5_0, D3D11)',
       'ANGLE (AMD Radeon R7 Graphics Direct3D9Ex vs_3_0 ps_3_0)',
       'ANGLE (AMD, AMD Radeon(TM) Graphics (0x0000164C) Direct3D11 vs_5_0 ps_5_0, D3D11)',
       'ANGLE (AMD, AMD Radeon(TM) Vega 8 Graphics (0x000015D8) Direct3D11 vs_5_0 ps_5_0, D3D11)']

renderer_list = {'Google Inc. (NVIDIA)': nvidia,
                 'Google Inc. (Intel)': intel,
                 'Google Inc. (AMD)': amd}

resolution_list = ['1360x768', '1920x1080', '2560x2048', '1856x1392', '1152x648', '1280x800', '1280x720', '2560x1440', '800x600',
                    '2880x1800', '1152x864', '2560x1600', '1400x1050', '1024x600', '1024x576', '1024x768', '1440x900', '1920x1440']



def get_font_list():
    min_length = 100
    max_length = len(all_font_list)
    new_list_len = random.randint(min_length, max_length)
    random.shuffle(all_font_list)
    font_list = []
    for i in range(new_list_len):
        elem = all_font_list[i]
        font_list.append(elem.replace("\n", ""))

    return font_list

async def generate_new_profile(email, proxy_host, proxy_name):
    proxy1 = {
	    'mode': 'http',
	    'host': proxy_host,
	    'port': 4444,
	    'username': 'ucKJZ5J',
	    'password': '6osSk0KzcdjgS0G',
	    'customName': proxy_name
        }
    vendor = random.choice(vendor_list)
    renderer = random.choice(renderer_list.get(vendor))

    API_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NzIzOGQyNjZmMDExYjAxODhmNmUwODciLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2NzJhNTBlMDc2YTdiNmEwNDZlMmM2YWYifQ.DR9kWiHGYCLMeJf6uY69AlmypdnWIGcSV7XidggCjTQ"
    API_URL = 'https://api.gologin.com/browser'
    proxy_dict = proxy1

    profile_data = {
        "name": email,
        "browserType": "chrome",
        "os": "win",
        "osSpec": "win11",
        "googleServicesEnabled": False,
        "lockEnabled": False,
        "debugMode": False,
        "navigator": {
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.6668.101 Safari/537.36",
            "resolution": random.choice(resolution_list),
            "platform": "Win32",
            "language": "'en-US,en;q=0.9'",
            "doNotTrack": False,
            "hardwareConcurrency": random.choice(cpu_thread_list),
            "deviceMemory": random.choice(ram_list),
            "maxTouchPoints": 0
        },
        "storage": {
            "local": True,
            "extensions": True,
            "bookmarks": True,
            "history": True,
            "passwords": True,
            "session": True,
            'indexedDb': True,
            'enableExternalExtensions': False
        },
        "proxyEnabled": True,
        "proxy": {
            "mode": "http",
            "host": proxy_dict['host'],
            "port": proxy_dict['port'],
            "username": proxy_dict['username'],
            "password": proxy_dict['password'],
            "customName": proxy_dict['customName'],
        },
        "audioContext": {
            "mode": "noise",
            "noise": random.uniform(1.358633218141e-08, 9.88415258481e-08)
        },
        "canvas": {
            "mode": "noise",
            "noise": random.uniform(0.10875213, 0.99741443)
        },
        "fonts": {
            "families": get_font_list(),
            "enableMasking": True,
            "enableDomRect": True
        },
        "mediaDevices": {
            "videoInputs": random.randint(0,1),
            "audioInputs": random.randint(0,1),
            "audioOutputs": random.randint(0,1),
            "enableMasking": True
        },
        "webRTC": {
            "mode": "alerted",
            "enabled": True,
            "customize": True,
            "localIpMasking": False,
            "fillBasedOnIp": True,
        },
        "webGL": {
            "mode": "noise",
            "getClientRectsNoise": random.uniform(1.13229, 9.83387),
            "noise": random.uniform(10.456, 99.893)
        },
        "clientRects": {
            "mode": "noise",
            "noise": random.uniform(1.13229, 9.83387)
        },
        "webGLMetadata": {
            "mode": "mask",
            "vendor": vendor,
            "renderer": renderer
        },
    }

    response = requests.post(
        API_URL,
        headers={"Authorization": f"Bearer {API_TOKEN}", "Content-Type": "application/json"},
        json=profile_data
    )

    if response.status_code in [200, 201]:
        profile = response.json()
        print("------------")
        print("Profile created successfully!")
        print("Profile ID:", profile.get("id"))
        print("------------")
    else:
        print("------------")
        print("Failed to create profile.")
        print("Status Code:", response.status_code)
        print("Response:", response.text)
        print("------------")
