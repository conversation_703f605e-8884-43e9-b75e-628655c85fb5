import datetime
from auto.ps_helper.mail_util import get_user_inbox, get_user_spam
import asyncio
import re
import pytz

class EmailConfirmationHandler:
    _email: str = None

    def __init__(self, email:str) -> None:
        self._email = email

    async def get_email_verification_link(self):
        print(f"getting email for {self._email}")
        link = None
        trycount = 0
        while link is None:
            if trycount > 36:
                raise Exception("Did not receive verification email")

            inbox_emails = get_user_inbox(self._email, search_criterion='(FROM "Sony")')
            spam_emails = get_user_spam(self._email, search_criterion='(FROM "Sony")')
            emails = sorted(
                inbox_emails + spam_emails, key=lambda d: d["received_datetime"]
            )
            latest_email = emails[-1]
            content = latest_email["content"]
            match = re.search(
                r"(?P<link>https://my\.account\.sony\.com/central/verification/\?[^ \r\n]+)",
                content,
                flags=re.MULTILINE,
            )
            if match:
                link = match.group("link").strip()
                print(f"Got the link: {link}")
                return link
            trycount += 1
            await asyncio.sleep(10)

async def get_link(email):
    return await EmailConfirmationHandler(email).get_email_verification_link()
    
if __name__ == '__main__':
    asyncio.run(get_link('<EMAIL>'))